const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const os = require('os');

// Create Express app
const app = express();
const server = http.createServer(app);

// Serve static files (HTML, CSS, JS)
app.use(express.static(__dirname));

// Health check endpoint for device discovery
app.get('/ping', (req, res) => {
  res.json({
    name: `Netflix Clone Receiver (${getLocalIP()})`,
    type: 'netflix-clone-receiver',
    version: '1.0.0',
    status: 'ready'
  });
});

// Create WebSocket server
const wss = new WebSocket.Server({ server });

let connectedClients = new Set();
let currentMedia = null;

wss.on('connection', (ws, req) => {
  console.log('New client connected from:', req.socket.remoteAddress);
  connectedClients.add(ws);

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'connected',
    message: 'Connected to Netflix Clone Receiver'
  }));

  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log('Received message:', data);
      
      handleMessage(ws, data);
    } catch (error) {
      console.error('Error parsing message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format'
      }));
    }
  });

  ws.on('close', () => {
    console.log('Client disconnected');
    connectedClients.delete(ws);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    connectedClients.delete(ws);
  });
});

function handleMessage(ws, message) {
  switch (message.type) {
    case 'connect':
      console.log('Client connected:', message.clientInfo);
      ws.send(JSON.stringify({ type: 'ready' }));
      break;

    case 'loadMedia':
      currentMedia = message.data;
      console.log('Loading media:', currentMedia.title);
      
      // Broadcast to all connected clients (including the HTML receiver)
      broadcast({
        type: 'loadMedia',
        data: currentMedia
      });
      
      ws.send(JSON.stringify({
        type: 'status',
        data: { message: 'Media loaded successfully' }
      }));
      break;

    case 'play':
      console.log('Play command received');
      broadcast({ type: 'play' });
      break;

    case 'pause':
      console.log('Pause command received');
      broadcast({ type: 'pause' });
      break;

    case 'seek':
      console.log('Seek command received:', message.data.time);
      broadcast({
        type: 'seek',
        data: message.data
      });
      break;

    case 'volume':
      console.log('Volume command received:', message.data.volume);
      broadcast({
        type: 'volume',
        data: message.data
      });
      break;

    case 'subtitle':
      console.log('Subtitle command received:', message.data.index);
      broadcast({
        type: 'subtitle',
        data: message.data
      });
      break;

    case 'stop':
      console.log('Stop command received');
      currentMedia = null;
      broadcast({ type: 'stop' });
      break;

    case 'playbackUpdate':
      // Forward playback updates from receiver to mobile app
      broadcast({
        type: 'playbackUpdate',
        data: message.data
      }, ws); // Exclude sender
      break;

    default:
      console.log('Unknown message type:', message.type);
      ws.send(JSON.stringify({
        type: 'error',
        data: { message: 'Unknown message type' }
      }));
  }
}

function broadcast(message, excludeWs = null) {
  const messageStr = JSON.stringify(message);
  
  connectedClients.forEach(client => {
    if (client !== excludeWs && client.readyState === WebSocket.OPEN) {
      client.send(messageStr);
    }
  });
}

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

const PORT = process.env.PORT || 8080;
const HOST = '0.0.0.0'; // Listen on all interfaces

server.listen(PORT, HOST, () => {
  const localIP = getLocalIP();
  console.log('🎬 Netflix Clone Casting Server Started!');
  console.log('📡 Server running on:');
  console.log(`   Local:    http://localhost:${PORT}`);
  console.log(`   Network:  http://${localIP}:${PORT}`);
  console.log('');
  console.log('📱 To use casting:');
  console.log(`   1. Open http://${localIP}:${PORT}/receiver.html on your TV/computer`);
  console.log('   2. Open Netflix Clone app on your phone');
  console.log('   3. Tap the cast button and select this device');
  console.log('');
  console.log('🔍 Device discovery endpoint: /ping');
  console.log('🌐 WebSocket server ready for connections');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down server...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('Shutting down server...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
