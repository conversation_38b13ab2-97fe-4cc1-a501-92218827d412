import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  StatusBar,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import castingService from '../services/CastingService';

const DeviceDiscoveryScreen = ({ route, navigation }) => {
  const { item, mediaType } = route.params;
  const insets = useSafeAreaInsets();
  
  const [devices, setDevices] = useState([]);
  const [isScanning, setIsScanning] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectingDeviceId, setConnectingDeviceId] = useState(null);

  useEffect(() => {
    startDeviceScan();
    
    // Set up event listeners
    const handleDeviceFound = (device) => {
      setDevices(prevDevices => {
        const existingIndex = prevDevices.findIndex(d => d.id === device.id);
        if (existingIndex >= 0) {
          const updatedDevices = [...prevDevices];
          updatedDevices[existingIndex] = device;
          return updatedDevices;
        } else {
          return [...prevDevices, device];
        }
      });
    };

    const handleScanStarted = () => {
      setIsScanning(true);
      setDevices([]);
    };

    const handleScanCompleted = (discoveredDevices) => {
      setIsScanning(false);
      setDevices(discoveredDevices);
    };

    const handleScanError = (error) => {
      setIsScanning(false);
      Alert.alert('Scan Error', 'Failed to scan for devices. Please try again.');
    };

    const handleConnected = (device) => {
      setIsConnecting(false);
      setConnectingDeviceId(null);
      
      // Navigate to remote control screen
      navigation.replace('RemoteControl', {
        item,
        mediaType,
        connectedDevice: device
      });
    };

    const handleConnectionError = (error) => {
      setIsConnecting(false);
      setConnectingDeviceId(null);
      Alert.alert('Connection Error', 'Failed to connect to device. Please try again.');
    };

    castingService.addEventListener('deviceFound', handleDeviceFound);
    castingService.addEventListener('scanStarted', handleScanStarted);
    castingService.addEventListener('scanCompleted', handleScanCompleted);
    castingService.addEventListener('scanError', handleScanError);
    castingService.addEventListener('connected', handleConnected);
    castingService.addEventListener('connectionError', handleConnectionError);

    return () => {
      castingService.removeEventListener('deviceFound', handleDeviceFound);
      castingService.removeEventListener('scanStarted', handleScanStarted);
      castingService.removeEventListener('scanCompleted', handleScanCompleted);
      castingService.removeEventListener('scanError', handleScanError);
      castingService.removeEventListener('connected', handleConnected);
      castingService.removeEventListener('connectionError', handleConnectionError);
      
      // Stop scanning when leaving screen
      castingService.stopScanning();
    };
  }, [item, mediaType, navigation]);

  const startDeviceScan = async () => {
    try {
      await castingService.scanForDevices();
    } catch (error) {
      console.error('Error starting device scan:', error);
      Alert.alert('Error', 'Failed to start device scan');
    }
  };

  const handleDevicePress = async (device) => {
    if (isConnecting) return;
    
    setIsConnecting(true);
    setConnectingDeviceId(device.id);
    
    try {
      await castingService.connectToDevice(device);
    } catch (error) {
      console.error('Error connecting to device:', error);
      setIsConnecting(false);
      setConnectingDeviceId(null);
      Alert.alert('Connection Failed', 'Could not connect to the selected device.');
    }
  };

  const handleRefresh = () => {
    startDeviceScan();
  };

  const renderDeviceItem = ({ item: device }) => {
    const isConnectingToThis = connectingDeviceId === device.id;
    
    return (
      <TouchableOpacity
        style={[
          slothStyles.deviceItem,
          isConnectingToThis && slothStyles.deviceItemConnecting
        ]}
        onPress={() => handleDevicePress(device)}
        disabled={isConnecting}
        activeOpacity={0.7}
      >
        <View style={slothStyles.deviceItemContent}>
          <View style={slothStyles.deviceIcon}>
            <Ionicons 
              name="tv" 
              size={32} 
              color={SLOTH_COLORS.primary} 
            />
          </View>
          
          <View style={slothStyles.deviceInfo}>
            <Text style={slothStyles.deviceName}>{device.name}</Text>
            <Text style={slothStyles.deviceDetails}>
              {device.ip} • {device.status}
            </Text>
          </View>
          
          <View style={slothStyles.deviceStatus}>
            {isConnectingToThis ? (
              <ActivityIndicator size="small" color={SLOTH_COLORS.primary} />
            ) : (
              <Ionicons 
                name="chevron-forward" 
                size={20} 
                color={SLOTH_COLORS.textSecondary} 
              />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={slothStyles.emptyDevicesContainer}>
      <Ionicons 
        name="tv-outline" 
        size={80} 
        color={SLOTH_COLORS.textSecondary} 
      />
      <Text style={slothStyles.emptyDevicesTitle}>
        {isScanning ? 'Scanning for devices...' : 'No devices found'}
      </Text>
      <Text style={slothStyles.emptyDevicesSubtitle}>
        {isScanning 
          ? 'Looking for available casting devices on your network'
          : 'Make sure your casting device is connected to the same network and try again'
        }
      </Text>
      {!isScanning && (
        <TouchableOpacity 
          style={slothStyles.emptyStateButton} 
          onPress={handleRefresh}
        >
          <Text style={slothStyles.emptyStateButtonText}>Scan Again</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={slothStyles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Header */}
      <View style={[slothStyles.screenHeader, { paddingTop: insets.top + 10 }]}>
        <TouchableOpacity 
          style={slothStyles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
        
        <Text style={slothStyles.screenTitle}>Cast to Device</Text>
        
        <TouchableOpacity 
          style={slothStyles.backButton} 
          onPress={handleRefresh}
          disabled={isScanning}
        >
          <Ionicons 
            name="refresh" 
            size={24} 
            color={isScanning ? SLOTH_COLORS.textSecondary : SLOTH_COLORS.white} 
          />
        </TouchableOpacity>
      </View>

      {/* Media Info */}
      <View style={slothStyles.castingMediaInfo}>
        <Text style={slothStyles.castingMediaTitle}>
          {item.title || item.name}
        </Text>
        <Text style={slothStyles.castingMediaSubtitle}>
          {mediaType === 'tv' ? 'TV Series' : 'Movie'}
        </Text>
      </View>

      {/* Device List */}
      <View style={slothStyles.deviceListContainer}>
        <FlatList
          data={devices}
          renderItem={renderDeviceItem}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={isScanning}
              onRefresh={handleRefresh}
              tintColor={SLOTH_COLORS.primary}
              colors={[SLOTH_COLORS.primary]}
            />
          }
          contentContainerStyle={[
            slothStyles.deviceListContent,
            devices.length === 0 && { flex: 1 }
          ]}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

export default DeviceDiscoveryScreen;
