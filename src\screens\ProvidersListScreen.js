// screens/ProvidersListScreen.js

import React, { memo } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  StyleSheet,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import { getProviders } from '../utils/providers';
import ProviderCard from '../components/ProviderCard';

const ProvidersListScreen = memo(({ navigation }) => {
  const insets = useSafeAreaInsets();
  const providers = getProviders();

  const handleProviderPress = (provider) => {
    // Navigate to the detail screen with the selected provider's data
     navigation.navigate('ProviderDetail', { providerId: provider.id });
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <View style={slothStyles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={[slothStyles.screenHeader, { paddingTop: insets.top + 10 }]}>
        <TouchableOpacity
          style={slothStyles.backButton}
          onPress={handleBackPress}
        >
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
        <Text style={slothStyles.screenTitle}>Streaming Providers</Text>
        <View style={slothStyles.headerSpacer} />
      </View>

      {/* Content */}
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingTop: 20,
          paddingBottom: insets.bottom + 20,
        }}
      >
        {/* Description */}
        <View style={styles.descriptionContainer}>
          <Text style={styles.descriptionText}>
            Explore content from your favorite streaming platforms.
          </Text>
        </View>

        {/* Providers List */}
        <View style={styles.providersContainer}>
          {providers.map((provider) => (
            <ProviderCard
              key={provider.id}
              provider={provider}
              onPress={handleProviderPress}
            />
          ))}
        </View>

        {/* Footer Info */}
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            Content availability may vary by region and subscription status.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
});

const styles = StyleSheet.create({
  descriptionContainer: {
    paddingHorizontal: 20,
    marginBottom: 20, // Adjusted margin
  },
  descriptionText: {
    color: SLOTH_COLORS.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  providersContainer: {
    alignItems: 'center', // Center cards horizontally
  },
  footerContainer: {
    paddingHorizontal: 20,
    marginTop: 10, // Adjusted margin to be closer to the cards
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  footerText: {
    color: SLOTH_COLORS.textSecondary,
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default ProvidersListScreen;