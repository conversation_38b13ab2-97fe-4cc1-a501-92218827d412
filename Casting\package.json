{"name": "netflix-clone-casting-server", "version": "1.0.0", "description": "Casting server for Netflix Clone React Native app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["casting", "netflix", "streaming", "websocket"], "author": "Netflix Clone", "license": "MIT"}