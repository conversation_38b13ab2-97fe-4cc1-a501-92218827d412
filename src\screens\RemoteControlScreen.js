import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StatusBar,
  Alert,
  Animated,
  Dimensions,
  Image,
  ActivityIndicator,
} from 'react-native';
import Slider from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import castingService from '../services/CastingService';
import tmdbApi from '../services/tmdbApi';
import soraApi from '../services/soraApi';
import { PROXY_BASE_URL } from '../utils/constants';

const { width } = Dimensions.get('window');

const RemoteControlScreen = ({ route, navigation }) => {
  const { item, mediaType, connectedDevice } = route.params;
  const insets = useSafeAreaInsets();
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1.0);
  const [isLoading, setIsLoading] = useState(false);
  const [subtitles, setSubtitles] = useState([]);
  const [currentSubtitleIndex, setCurrentSubtitleIndex] = useState(-1);
  const [showSubtitleMenu, setShowSubtitleMenu] = useState(false);
  const [isSeeking, setIsSeeking] = useState(false);
  const [showEpisodeMenu, setShowEpisodeMenu] = useState(false);
  const [availableSeasons, setAvailableSeasons] = useState([]);
  const [availableEpisodes, setAvailableEpisodes] = useState([]);
  const [currentSeason, setCurrentSeason] = useState(route.params.season);
  const [currentEpisode, setCurrentEpisode] = useState(route.params.episode);
  
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Set up event listeners for casting updates
    const handlePlaybackUpdate = (data) => {
      console.log('Received playback update:', data);
      if (data.isPlaying !== undefined) setIsPlaying(data.isPlaying);
      // Only update currentTime if user is not actively seeking
      if (data.currentTime !== undefined && !isSeeking) {
        setCurrentTime(data.currentTime);
      }
      if (data.duration !== undefined && data.duration > 0) {
        setDuration(data.duration);
        console.log('Duration set to:', data.duration);
      }
    };

    const handleDisconnected = () => {
      Alert.alert(
        'Connection Lost',
        'Lost connection to casting device',
        [
          {
            text: 'Reconnect',
            onPress: () => navigation.replace('DeviceDiscovery', { item, mediaType }),
          },
          {
            text: 'Cancel',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    };

    const handleReceiverError = (error) => {
      Alert.alert('Playback Error', error.message || 'An error occurred during playback');
    };

    castingService.addEventListener('playbackUpdate', handlePlaybackUpdate);
    castingService.addEventListener('disconnected', handleDisconnected);
    castingService.addEventListener('receiverError', handleReceiverError);

    // Start casting the media
    startCasting();

    // Load season/episode data for TV shows
    if (mediaType === 'tv') {
      loadSeasonEpisodeData();
    }

    return () => {
      castingService.removeEventListener('playbackUpdate', handlePlaybackUpdate);
      castingService.removeEventListener('disconnected', handleDisconnected);
      castingService.removeEventListener('receiverError', handleReceiverError);
    };
  }, [item, mediaType, navigation]);

  const loadSeasonEpisodeData = async () => {
    try {
      // Get all seasons for this show
      const seasonsData = await tmdbApi.getTVSeasons(item.id);
      setAvailableSeasons(seasonsData || []);

      // Get episodes for current season
      if (currentSeason) {
        const episodesData = await tmdbApi.getSeasonEpisodes(item.id, currentSeason.season_number);
        setAvailableEpisodes(episodesData || []);
      }
    } catch (error) {
      console.error('Error loading season/episode data:', error);
    }
  };

  const startCasting = async () => {
    setIsLoading(true);

    try {
      // Check if still connected
      if (!castingService.isConnected()) {
        throw new Error('Lost connection to casting device');
      }

      // Get streaming data using the correct API calls
      const streamData = mediaType === 'movie'
        ? await soraApi.getMovieStreams(item.id)
        : await soraApi.getTVStreams(
            item.id,
            route.params.season?.season_number || 1,
            route.params.episode?.episode_number || 1
          );

      const processedData = soraApi.processStreamResponse(streamData);

      if (processedData && processedData.streams && processedData.streams.length > 0) {
        const bestStream = soraApi.getBestQualityStream(processedData);
        const subtitles = processedData.subtitles || [];

        setSubtitles(subtitles);

        // Use the proxy URL like in PlayerScreen
        const proxiedStreamUrl = `${PROXY_BASE_URL}${encodeURIComponent(bestStream.url)}`;

        const mediaInfo = {
          streamUrl: proxiedStreamUrl || bestStream.url,
          title: item.title || item.name,
          subtitle: mediaType === 'tv'
            ? `Season ${route.params.season?.season_number || 1}, Episode ${route.params.episode?.episode_number || 1}`
            : new Date(item.release_date || item.first_air_date).getFullYear().toString(),
          poster: tmdbApi.getPosterUrl(item.poster_path),
          subtitles: subtitles,
          mediaType: mediaType,
          season: route.params.season,
          episode: route.params.episode
        };

        const success = await castingService.castMedia(mediaInfo);
        if (!success) {
          throw new Error('Failed to send media to casting device');
        }

        console.log('Media casting initiated, waiting for duration...');
      } else {
        throw new Error('No streaming data available for this content');
      }
    } catch (error) {
      console.error('Error starting cast:', error);
      Alert.alert(
        'Casting Error',
        error.message || 'Failed to start casting. Please try again.',
        [
          { text: 'Retry', onPress: startCasting },
          { text: 'Cancel', onPress: () => navigation.goBack() }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlayPause = () => {
    const animateButton = () => {
      Animated.sequence([
        Animated.timing(scaleAnim, { toValue: 0.9, duration: 100, useNativeDriver: true }),
        Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
      ]).start();
    };

    animateButton();
    
    if (isPlaying) {
      castingService.pause();
    } else {
      castingService.play();
    }
  };

  const handleSeek = (value) => {
    console.log('Seeking to:', value, 'Duration:', duration);
    setCurrentTime(value);
    castingService.seek(value);
  };

  const handleSeekStart = () => {
    setIsSeeking(true);
  };

  const handleSeekComplete = (value) => {
    setIsSeeking(false);
    handleSeek(value);
  };

  const handleVolumeChange = (value) => {
    setVolume(value);
    castingService.setVolume(value);
  };

  const handleSubtitleSelect = (index) => {
    console.log('Selecting subtitle index:', index);
    setCurrentSubtitleIndex(index);
    setShowSubtitleMenu(false);

    const success = castingService.setSubtitle(index);
    if (!success) {
      Alert.alert('Error', 'Failed to change subtitles. Please try again.');
    } else {
      console.log('Subtitle selection sent to receiver');
    }
  };

  const handleEpisodeChange = async (episode) => {
    try {
      setIsLoading(true);
      setCurrentEpisode(episode);
      setShowEpisodeMenu(false);

      // Get new streaming data for the selected episode
      const streamData = await soraApi.getTVStreams(item.id, currentSeason.season_number, episode.episode_number);
      const processedData = soraApi.processStreamResponse(streamData);

      if (processedData && processedData.streams && processedData.streams.length > 0) {
        const bestStream = soraApi.getBestQualityStream(processedData);
        const proxiedStreamUrl = `${PROXY_BASE_URL}${encodeURIComponent(bestStream.url)}`;

        const mediaInfo = {
          streamUrl: proxiedStreamUrl || bestStream.url,
          title: item.title || item.name,
          subtitle: `Season ${currentSeason.season_number}, Episode ${episode.episode_number}: ${episode.name}`,
          poster: tmdbApi.getPosterUrl(item.poster_path),
          subtitles: processedData.subtitles || [],
          mediaType: mediaType,
          season: currentSeason,
          episode: episode
        };

        await castingService.castMedia(mediaInfo);
        setSubtitles(processedData.subtitles || []);
      }
    } catch (error) {
      console.error('Error changing episode:', error);
      Alert.alert('Error', 'Failed to change episode. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSeasonChange = async (season) => {
    try {
      setIsLoading(true);
      setCurrentSeason(season);

      // Load episodes for the new season
      const episodesData = await tmdbApi.getSeasonEpisodes(item.id, season.season_number);
      setAvailableEpisodes(episodesData || []);

      // Auto-select first episode of the new season
      if (episodesData && episodesData.length > 0) {
        await handleEpisodeChange(episodesData[0]);
      }
    } catch (error) {
      console.error('Error changing season:', error);
      Alert.alert('Error', 'Failed to change season. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStop = () => {
    Alert.alert(
      'Stop Casting',
      'Are you sure you want to stop casting?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Stop',
          style: 'destructive',
          onPress: () => {
            castingService.stop();
            navigation.goBack();
          },
        },
      ]
    );
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progress = duration > 0 ? currentTime / duration : 0;

  return (
    <View style={slothStyles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Header */}
      <View style={[slothStyles.remoteHeader, { paddingTop: insets.top + 10 }]}>
        <TouchableOpacity 
          style={slothStyles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
        
        <View style={slothStyles.remoteHeaderInfo}>
          <Text style={slothStyles.remoteHeaderTitle}>Casting to</Text>
          <Text style={slothStyles.remoteHeaderDevice}>{connectedDevice?.name}</Text>
        </View>
        
        <TouchableOpacity 
          style={slothStyles.backButton} 
          onPress={handleStop}
        >
          <Ionicons name="stop" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
      </View>

      {/* Media Info */}
      <View style={slothStyles.remoteMediaInfo}>
        <Image
          source={{ uri: tmdbApi.getPosterUrl(item.poster_path) }}
          style={slothStyles.remoteMediaPoster}
        />
        <View style={slothStyles.remoteMediaDetails}>
          <Text style={slothStyles.remoteMediaTitle} numberOfLines={2}>
            {item.title || item.name}
          </Text>
          <Text style={slothStyles.remoteMediaSubtitle}>
            {mediaType === 'tv'
              ? `Season ${currentSeason?.season_number || 1}, Episode ${currentEpisode?.episode_number || 1}${currentEpisode?.name ? `: ${currentEpisode.name}` : ''}`
              : new Date(item.release_date || item.first_air_date).getFullYear()
            }
          </Text>
          {item.vote_average > 0 && (
            <View style={slothStyles.remoteMediaRating}>
              <Ionicons name="star" size={14} color={SLOTH_COLORS.primary} />
              <Text style={slothStyles.remoteMediaRatingText}>
                {item.vote_average.toFixed(1)}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Progress Bar */}
      <View style={slothStyles.remoteProgressContainer}>
        <Text style={slothStyles.remoteTimeText}>{formatTime(currentTime)}</Text>
        <View style={slothStyles.remoteProgressBarContainer}>
          <Slider
            style={slothStyles.remoteProgressBar}
            minimumValue={0}
            maximumValue={duration || 100}
            value={currentTime}
            onValueChange={setCurrentTime}
            onSlidingStart={handleSeekStart}
            onSlidingComplete={handleSeekComplete}
            minimumTrackTintColor={SLOTH_COLORS.primary}
            maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
            thumbStyle={slothStyles.remoteProgressThumb}
            disabled={duration <= 0}
          />
        </View>
        <Text style={slothStyles.remoteTimeText}>{formatTime(duration)}</Text>
      </View>

      {/* Main Controls */}
      <View style={slothStyles.remoteMainControls}>
        <TouchableOpacity
          style={slothStyles.remoteControlButton}
          onPress={() => handleSeek(Math.max(0, currentTime - 10))}
          disabled={duration <= 0}
        >
          <Ionicons name="play-back" size={32} color={duration <= 0 ? SLOTH_COLORS.textSecondary : SLOTH_COLORS.white} />
        </TouchableOpacity>

        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <TouchableOpacity 
            style={slothStyles.remotePlayPauseButton}
            onPress={handlePlayPause}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="large" color={SLOTH_COLORS.white} />
            ) : (
              <Ionicons 
                name={isPlaying ? "pause" : "play"} 
                size={40} 
                color={SLOTH_COLORS.white} 
              />
            )}
          </TouchableOpacity>
        </Animated.View>

        <TouchableOpacity
          style={slothStyles.remoteControlButton}
          onPress={() => handleSeek(Math.min(duration || 0, currentTime + 10))}
          disabled={duration <= 0}
        >
          <Ionicons name="play-forward" size={32} color={duration <= 0 ? SLOTH_COLORS.textSecondary : SLOTH_COLORS.white} />
        </TouchableOpacity>
      </View>

      {/* Secondary Controls */}
      <View style={slothStyles.remoteSecondaryControls}>
        {/* Volume Control */}
        <View style={slothStyles.remoteVolumeContainer}>
          <Ionicons name="volume-low" size={20} color={SLOTH_COLORS.textSecondary} />
          <Slider
            style={slothStyles.remoteVolumeSlider}
            minimumValue={0}
            maximumValue={1}
            value={volume}
            onValueChange={handleVolumeChange}
            minimumTrackTintColor={SLOTH_COLORS.primary}
            maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
          />
          <Ionicons name="volume-high" size={20} color={SLOTH_COLORS.textSecondary} />
        </View>

        {/* Episode/Season Control for TV Shows */}
        {mediaType === 'tv' && (
          <TouchableOpacity
            style={slothStyles.remoteSubtitleButton}
            onPress={() => setShowEpisodeMenu(true)}
          >
            <Ionicons name="list-outline" size={24} color={SLOTH_COLORS.white} />
            <Text style={slothStyles.remoteSubtitleButtonText}>Episodes</Text>
          </TouchableOpacity>
        )}

        {/* Subtitle Control */}
        {subtitles.length > 0 && (
          <TouchableOpacity
            style={[slothStyles.remoteSubtitleButton, { marginTop: 10 }]}
            onPress={() => setShowSubtitleMenu(true)}
          >
            <Ionicons name="chatbox-outline" size={24} color={SLOTH_COLORS.white} />
            <Text style={slothStyles.remoteSubtitleButtonText}>Subtitles</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Episode/Season Menu Modal */}
      {showEpisodeMenu && (
        <View style={slothStyles.remoteSubtitleMenu}>
          <View style={slothStyles.remoteSubtitleMenuContent}>
            <Text style={slothStyles.remoteSubtitleMenuTitle}>
              {currentSeason ? `Season ${currentSeason.season_number} Episodes` : 'Episodes'}
            </Text>

            {/* Season Selector */}
            {availableSeasons.length > 1 && (
              <View style={slothStyles.seasonSelector}>
                <Text style={slothStyles.seasonSelectorTitle}>Season:</Text>
                <View style={slothStyles.seasonButtons}>
                  {availableSeasons.map((season) => (
                    <TouchableOpacity
                      key={season.season_number}
                      style={[
                        slothStyles.seasonButton,
                        currentSeason?.season_number === season.season_number && slothStyles.seasonButtonActive
                      ]}
                      onPress={() => handleSeasonChange(season)}
                    >
                      <Text style={[
                        slothStyles.seasonButtonText,
                        currentSeason?.season_number === season.season_number && slothStyles.seasonButtonTextActive
                      ]}>
                        {season.season_number}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            {/* Episode List */}
            <View style={slothStyles.episodeList}>
              {availableEpisodes.map((episode) => (
                <TouchableOpacity
                  key={episode.episode_number}
                  style={[
                    slothStyles.remoteSubtitleMenuItem,
                    currentEpisode?.episode_number === episode.episode_number && slothStyles.remoteSubtitleMenuItemActive
                  ]}
                  onPress={() => handleEpisodeChange(episode)}
                >
                  <View style={slothStyles.episodeInfo}>
                    <Text style={slothStyles.episodeNumber}>E{episode.episode_number}</Text>
                    <View style={slothStyles.episodeDetails}>
                      <Text style={slothStyles.episodeTitle} numberOfLines={1}>
                        {episode.name || `Episode ${episode.episode_number}`}
                      </Text>
                      {episode.overview && (
                        <Text style={slothStyles.episodeOverview} numberOfLines={2}>
                          {episode.overview}
                        </Text>
                      )}
                    </View>
                  </View>
                  {currentEpisode?.episode_number === episode.episode_number && (
                    <Ionicons name="checkmark" size={20} color={SLOTH_COLORS.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            <TouchableOpacity
              style={slothStyles.remoteSubtitleMenuClose}
              onPress={() => setShowEpisodeMenu(false)}
            >
              <Text style={slothStyles.remoteSubtitleMenuCloseText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Subtitle Menu Modal */}
      {showSubtitleMenu && (
        <View style={slothStyles.remoteSubtitleMenu}>
          <View style={slothStyles.remoteSubtitleMenuContent}>
            <Text style={slothStyles.remoteSubtitleMenuTitle}>Select Subtitles</Text>

            <TouchableOpacity
              style={[
                slothStyles.remoteSubtitleMenuItem,
                currentSubtitleIndex === -1 && slothStyles.remoteSubtitleMenuItemActive
              ]}
              onPress={() => handleSubtitleSelect(-1)}
            >
              <Text style={slothStyles.remoteSubtitleMenuItemText}>Off</Text>
              {currentSubtitleIndex === -1 && (
                <Ionicons name="checkmark" size={20} color={SLOTH_COLORS.primary} />
              )}
            </TouchableOpacity>

            {subtitles.map((subtitle, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  slothStyles.remoteSubtitleMenuItem,
                  currentSubtitleIndex === index && slothStyles.remoteSubtitleMenuItemActive
                ]}
                onPress={() => handleSubtitleSelect(index)}
              >
                <Text style={slothStyles.remoteSubtitleMenuItemText}>
                  {subtitle.label || subtitle.language || subtitle.name || `Subtitle ${index + 1}`}
                </Text>
                {currentSubtitleIndex === index && (
                  <Ionicons name="checkmark" size={20} color={SLOTH_COLORS.primary} />
                )}
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              style={slothStyles.remoteSubtitleMenuClose}
              onPress={() => setShowSubtitleMenu(false)}
            >
              <Text style={slothStyles.remoteSubtitleMenuCloseText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default RemoteControlScreen;
