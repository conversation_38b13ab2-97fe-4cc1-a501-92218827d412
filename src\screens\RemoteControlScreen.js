import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Slider,
  StatusBar,
  Alert,
  Animated,
  Dimensions,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import castingService from '../services/CastingService';
import tmdbApi from '../services/tmdbApi';
import soraApi from '../services/soraApi';

const { width } = Dimensions.get('window');

const RemoteControlScreen = ({ route, navigation }) => {
  const { item, mediaType, connectedDevice } = route.params;
  const insets = useSafeAreaInsets();
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1.0);
  const [isLoading, setIsLoading] = useState(false);
  const [subtitles, setSubtitles] = useState([]);
  const [currentSubtitleIndex, setCurrentSubtitleIndex] = useState(-1);
  const [showSubtitleMenu, setShowSubtitleMenu] = useState(false);
  
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Set up event listeners for casting updates
    const handlePlaybackUpdate = (data) => {
      if (data.isPlaying !== undefined) setIsPlaying(data.isPlaying);
      if (data.currentTime !== undefined) setCurrentTime(data.currentTime);
      if (data.duration !== undefined) setDuration(data.duration);
    };

    const handleDisconnected = () => {
      Alert.alert(
        'Connection Lost',
        'Lost connection to casting device',
        [
          {
            text: 'Reconnect',
            onPress: () => navigation.replace('DeviceDiscovery', { item, mediaType }),
          },
          {
            text: 'Cancel',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    };

    const handleReceiverError = (error) => {
      Alert.alert('Playback Error', error.message || 'An error occurred during playback');
    };

    castingService.addEventListener('playbackUpdate', handlePlaybackUpdate);
    castingService.addEventListener('disconnected', handleDisconnected);
    castingService.addEventListener('receiverError', handleReceiverError);

    // Start casting the media
    startCasting();

    return () => {
      castingService.removeEventListener('playbackUpdate', handlePlaybackUpdate);
      castingService.removeEventListener('disconnected', handleDisconnected);
      castingService.removeEventListener('receiverError', handleReceiverError);
    };
  }, [item, mediaType, navigation]);

  const startCasting = async () => {
    setIsLoading(true);

    try {
      // Check if still connected
      if (!castingService.isConnected()) {
        throw new Error('Lost connection to casting device');
      }

      // Get streaming data
      const streamData = await soraApi.getStreamingData(item, mediaType);
      const processedData = soraApi.processStreamResponse(streamData);

      if (processedData && processedData.streams && processedData.streams.length > 0) {
        const bestStream = soraApi.getBestQualityStream(processedData);
        const subtitles = processedData.subtitles || [];

        setSubtitles(subtitles);

        const mediaInfo = {
          streamUrl: bestStream.url,
          title: item.title || item.name,
          subtitle: mediaType === 'tv' ? `Season ${route.params.season?.season_number || 1}, Episode ${route.params.episode?.episode_number || 1}` : new Date(item.release_date || item.first_air_date).getFullYear().toString(),
          poster: tmdbApi.getPosterUrl(item.poster_path),
          subtitles: subtitles,
          mediaType: mediaType,
          season: route.params.season,
          episode: route.params.episode
        };

        const success = await castingService.castMedia(mediaInfo);
        if (!success) {
          throw new Error('Failed to send media to casting device');
        }
      } else {
        throw new Error('No streaming data available for this content');
      }
    } catch (error) {
      console.error('Error starting cast:', error);
      Alert.alert(
        'Casting Error',
        error.message || 'Failed to start casting. Please try again.',
        [
          { text: 'Retry', onPress: startCasting },
          { text: 'Cancel', onPress: () => navigation.goBack() }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlayPause = () => {
    const animateButton = () => {
      Animated.sequence([
        Animated.timing(scaleAnim, { toValue: 0.9, duration: 100, useNativeDriver: true }),
        Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
      ]).start();
    };

    animateButton();
    
    if (isPlaying) {
      castingService.pause();
    } else {
      castingService.play();
    }
  };

  const handleSeek = (value) => {
    setCurrentTime(value);
    castingService.seek(value);
  };

  const handleVolumeChange = (value) => {
    setVolume(value);
    castingService.setVolume(value);
  };

  const handleSubtitleSelect = (index) => {
    setCurrentSubtitleIndex(index);
    setShowSubtitleMenu(false);
    castingService.setSubtitle(index);
  };

  const handleStop = () => {
    Alert.alert(
      'Stop Casting',
      'Are you sure you want to stop casting?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Stop',
          style: 'destructive',
          onPress: () => {
            castingService.stop();
            navigation.goBack();
          },
        },
      ]
    );
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progress = duration > 0 ? currentTime / duration : 0;

  return (
    <View style={slothStyles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Header */}
      <View style={[slothStyles.remoteHeader, { paddingTop: insets.top + 10 }]}>
        <TouchableOpacity 
          style={slothStyles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
        
        <View style={slothStyles.remoteHeaderInfo}>
          <Text style={slothStyles.remoteHeaderTitle}>Casting to</Text>
          <Text style={slothStyles.remoteHeaderDevice}>{connectedDevice?.name}</Text>
        </View>
        
        <TouchableOpacity 
          style={slothStyles.backButton} 
          onPress={handleStop}
        >
          <Ionicons name="stop" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
      </View>

      {/* Media Info */}
      <View style={slothStyles.remoteMediaInfo}>
        <Image
          source={{ uri: tmdbApi.getPosterUrl(item.poster_path) }}
          style={slothStyles.remoteMediaPoster}
        />
        <View style={slothStyles.remoteMediaDetails}>
          <Text style={slothStyles.remoteMediaTitle} numberOfLines={2}>
            {item.title || item.name}
          </Text>
          <Text style={slothStyles.remoteMediaSubtitle}>
            {mediaType === 'tv' 
              ? `Season ${route.params.season?.season_number || 1}, Episode ${route.params.episode?.episode_number || 1}`
              : new Date(item.release_date || item.first_air_date).getFullYear()
            }
          </Text>
          {item.vote_average > 0 && (
            <View style={slothStyles.remoteMediaRating}>
              <Ionicons name="star" size={14} color={SLOTH_COLORS.primary} />
              <Text style={slothStyles.remoteMediaRatingText}>
                {item.vote_average.toFixed(1)}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Progress Bar */}
      <View style={slothStyles.remoteProgressContainer}>
        <Text style={slothStyles.remoteTimeText}>{formatTime(currentTime)}</Text>
        <View style={slothStyles.remoteProgressBarContainer}>
          <Slider
            style={slothStyles.remoteProgressBar}
            minimumValue={0}
            maximumValue={duration}
            value={currentTime}
            onValueChange={handleSeek}
            minimumTrackTintColor={SLOTH_COLORS.primary}
            maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
            thumbStyle={slothStyles.remoteProgressThumb}
          />
        </View>
        <Text style={slothStyles.remoteTimeText}>{formatTime(duration)}</Text>
      </View>

      {/* Main Controls */}
      <View style={slothStyles.remoteMainControls}>
        <TouchableOpacity 
          style={slothStyles.remoteControlButton}
          onPress={() => handleSeek(Math.max(0, currentTime - 10))}
        >
          <Ionicons name="play-back" size={32} color={SLOTH_COLORS.white} />
        </TouchableOpacity>

        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <TouchableOpacity 
            style={slothStyles.remotePlayPauseButton}
            onPress={handlePlayPause}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="large" color={SLOTH_COLORS.white} />
            ) : (
              <Ionicons 
                name={isPlaying ? "pause" : "play"} 
                size={40} 
                color={SLOTH_COLORS.white} 
              />
            )}
          </TouchableOpacity>
        </Animated.View>

        <TouchableOpacity 
          style={slothStyles.remoteControlButton}
          onPress={() => handleSeek(Math.min(duration, currentTime + 10))}
        >
          <Ionicons name="play-forward" size={32} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
      </View>

      {/* Secondary Controls */}
      <View style={slothStyles.remoteSecondaryControls}>
        {/* Volume Control */}
        <View style={slothStyles.remoteVolumeContainer}>
          <Ionicons name="volume-low" size={20} color={SLOTH_COLORS.textSecondary} />
          <Slider
            style={slothStyles.remoteVolumeSlider}
            minimumValue={0}
            maximumValue={1}
            value={volume}
            onValueChange={handleVolumeChange}
            minimumTrackTintColor={SLOTH_COLORS.primary}
            maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
          />
          <Ionicons name="volume-high" size={20} color={SLOTH_COLORS.textSecondary} />
        </View>

        {/* Subtitle Control */}
        {subtitles.length > 0 && (
          <TouchableOpacity 
            style={slothStyles.remoteSubtitleButton}
            onPress={() => setShowSubtitleMenu(true)}
          >
            <Ionicons name="chatbox-outline" size={24} color={SLOTH_COLORS.white} />
            <Text style={slothStyles.remoteSubtitleButtonText}>Subtitles</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Subtitle Menu Modal */}
      {showSubtitleMenu && (
        <View style={slothStyles.remoteSubtitleMenu}>
          <View style={slothStyles.remoteSubtitleMenuContent}>
            <Text style={slothStyles.remoteSubtitleMenuTitle}>Select Subtitles</Text>
            
            <TouchableOpacity 
              style={[
                slothStyles.remoteSubtitleMenuItem,
                currentSubtitleIndex === -1 && slothStyles.remoteSubtitleMenuItemActive
              ]}
              onPress={() => handleSubtitleSelect(-1)}
            >
              <Text style={slothStyles.remoteSubtitleMenuItemText}>Off</Text>
              {currentSubtitleIndex === -1 && (
                <Ionicons name="checkmark" size={20} color={SLOTH_COLORS.primary} />
              )}
            </TouchableOpacity>

            {subtitles.map((subtitle, index) => (
              <TouchableOpacity 
                key={index}
                style={[
                  slothStyles.remoteSubtitleMenuItem,
                  currentSubtitleIndex === index && slothStyles.remoteSubtitleMenuItemActive
                ]}
                onPress={() => handleSubtitleSelect(index)}
              >
                <Text style={slothStyles.remoteSubtitleMenuItemText}>
                  {subtitle.label || `Subtitle ${index + 1}`}
                </Text>
                {currentSubtitleIndex === index && (
                  <Ionicons name="checkmark" size={20} color={SLOTH_COLORS.primary} />
                )}
              </TouchableOpacity>
            ))}

            <TouchableOpacity 
              style={slothStyles.remoteSubtitleMenuClose}
              onPress={() => setShowSubtitleMenu(false)}
            >
              <Text style={slothStyles.remoteSubtitleMenuCloseText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default RemoteControlScreen;
