// components/ProviderCard.js
import React, { memo, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { SLOTH_COLORS } from '../styles/sloth';

const { width } = Dimensions.get('window');

const ProviderCard = memo(({ provider, onPress }) => {
  const scaleValue = useMemo(() => new Animated.Value(1), []);
  const { LogoComponent } = provider;

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.97,
      useNativeDriver: true,
      tension: 400,
      friction: 15,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 400,
      friction: 15,
    }).start();
  };

  const handlePress = () => {
    onPress(provider);
  };

  const cardWidth = width * 0.9;

  return (
    <Animated.View style={[styles.container, { transform: [{ scale: scaleValue }], width: cardWidth }]}>
      <TouchableOpacity
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.95}
        style={styles.touchable}
      >
        <LinearGradient
          colors={[provider.theme.gradientStart, provider.theme.gradientEnd]}
          start={{ x: 0.5, y: 0 }}
          end={{ x: 0.5, y: 1 }}
          style={styles.gradient}
        >
          <View style={styles.logoContainer}>
            <LogoComponent />
          </View>

          <View style={styles.infoContainer}>
            {/* Removed provider.name here */}
            <Text style={styles.providerDescription}>{provider.description}</Text>
            {provider.hasOriginals && (
              <View style={[styles.originalsBadge, { backgroundColor: provider.theme.primary + '25' }]}>
                <Text style={[styles.originalsBadgeText, { color: provider.theme.primary }]}>
                  Originals & Exclusives
                </Text>
              </View>
            )}
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  container: {
    marginBottom: 25,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 10,
  },
  touchable: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  gradient: {
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    overflow: 'hidden',
    alignItems: 'center',
    padding: 25,
  },
  logoContainer: {
    height: 70,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 25,
  },
  infoContainer: {
    alignItems: 'center',
    width: '100%',
  },
  providerDescription: {
    color: SLOTH_COLORS.textSecondary,
    fontSize: 15,
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 10,
  },
  originalsBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  originalsBadgeText: {
    fontSize: 12,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
});

export default ProviderCard;
