<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netflix Clone - Casting Receiver</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <!-- Idle State -->
        <div id="idle-screen" class="screen active">
            <div class="idle-content">
                <div class="logo">
                    <div class="logo-icon">N</div>
                    <h1>Netflix Clone</h1>
                </div>
                <div class="status-info">
                    <div class="device-info">
                        <h2 id="device-name">Casting Device</h2>
                        <p id="device-ip">Ready to cast</p>
                    </div>
                    <div class="connection-status">
                        <div id="status-indicator" class="status-dot waiting"></div>
                        <span id="status-text">Waiting for connection...</span>
                    </div>
                </div>
                <div class="instructions">
                    <p>Open Netflix Clone app on your mobile device</p>
                    <p>Tap the cast button and select this device</p>
                </div>
            </div>
        </div>

        <!-- Media Player -->
        <div id="player-screen" class="screen">
            <video id="video-player" controls>
                <source id="video-source" src="" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            
            <!-- Subtitle Overlay -->
            <div id="subtitle-overlay" class="subtitle-overlay">
                <div id="subtitle-text" class="subtitle-text"></div>
            </div>
            
            <!-- Media Info Overlay -->
            <div id="media-info" class="media-info">
                <div class="media-poster">
                    <img id="media-poster-img" src="" alt="Media Poster">
                </div>
                <div class="media-details">
                    <h2 id="media-title">Media Title</h2>
                    <p id="media-subtitle">Season 1, Episode 1</p>
                </div>
                <div class="playback-controls">
                    <button id="play-pause-btn" class="control-btn">
                        <span class="play-icon">▶</span>
                        <span class="pause-icon" style="display: none;">⏸</span>
                    </button>
                </div>
            </div>
            
            <!-- Loading Overlay -->
            <div id="loading-overlay" class="loading-overlay">
                <div class="loading-spinner"></div>
                <p>Loading media...</p>
            </div>
        </div>

        <!-- Error Screen -->
        <div id="error-screen" class="screen">
            <div class="error-content">
                <div class="error-icon">⚠</div>
                <h2>Playback Error</h2>
                <p id="error-message">An error occurred while playing the media.</p>
                <button id="retry-btn" class="retry-button">Try Again</button>
            </div>
        </div>
    </div>

    <!-- WebSocket and Media Control Script -->
    <script>
        // Prevent external script errors from affecting our app
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('share-modal.js')) {
                e.preventDefault();
                return false;
            }
        });
    </script>
    <script src="receiver.js"></script>
</body>
</html>
