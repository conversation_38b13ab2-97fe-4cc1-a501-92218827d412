// Netflix Clone Casting Receiver
class CastingReceiver {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.currentMedia = null;
        this.subtitles = [];
        this.currentSubtitleIndex = -1;
        this.mediaInfoTimeout = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.startWebSocketServer();
        this.updateDeviceInfo();
    }

    initializeElements() {
        // Screen elements
        this.idleScreen = document.getElementById('idle-screen');
        this.playerScreen = document.getElementById('player-screen');
        this.errorScreen = document.getElementById('error-screen');
        
        // Player elements
        this.videoPlayer = document.getElementById('video-player');
        this.videoSource = document.getElementById('video-source');
        this.subtitleOverlay = document.getElementById('subtitle-overlay');
        this.subtitleText = document.getElementById('subtitle-text');
        
        // Media info elements
        this.mediaInfo = document.getElementById('media-info');
        this.mediaPosterImg = document.getElementById('media-poster-img');
        this.mediaTitle = document.getElementById('media-title');
        this.mediaSubtitle = document.getElementById('media-subtitle');
        this.playPauseBtn = document.getElementById('play-pause-btn');
        
        // Status elements
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusText = document.getElementById('status-text');
        this.deviceName = document.getElementById('device-name');
        this.deviceIP = document.getElementById('device-ip');
        
        // Loading and error elements
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.errorMessage = document.getElementById('error-message');
        this.retryBtn = document.getElementById('retry-btn');
    }

    setupEventListeners() {
        // Video player events
        this.videoPlayer.addEventListener('loadstart', () => this.showLoading());
        this.videoPlayer.addEventListener('canplay', () => this.hideLoading());
        this.videoPlayer.addEventListener('play', () => this.updatePlayPauseButton(true));
        this.videoPlayer.addEventListener('pause', () => this.updatePlayPauseButton(false));
        this.videoPlayer.addEventListener('timeupdate', () => this.handleTimeUpdate());
        this.videoPlayer.addEventListener('error', (e) => this.handleVideoError(e));
        this.videoPlayer.addEventListener('ended', () => this.handleVideoEnded());
        
        // Control button events
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        this.retryBtn.addEventListener('click', () => this.retryConnection());
        
        // Mouse/touch events for media info
        this.playerScreen.addEventListener('mousemove', () => this.showMediaInfo());
        this.playerScreen.addEventListener('click', () => this.showMediaInfo());
        this.playerScreen.addEventListener('touchstart', () => this.showMediaInfo());
    }

    updateDeviceInfo() {
        // Get device information
        const deviceName = `Casting Device (${window.location.hostname})`;
        const deviceIP = window.location.hostname;
        
        this.deviceName.textContent = deviceName;
        this.deviceIP.textContent = `IP: ${deviceIP}:8080`;
    }

    startWebSocketServer() {
        try {
            // For browser-based WebSocket server, we'll use a simple approach
            // In a real implementation, you'd need a proper WebSocket server
            this.setupWebSocketConnection();
        } catch (error) {
            console.error('Error starting WebSocket server:', error);
            this.updateStatus('error', 'Failed to start casting service');
        }
    }

    setupWebSocketConnection() {
        this.updateStatus('waiting', 'Waiting for connection...');
        this.connectToWebSocketServer();
    }

    connectToWebSocketServer() {
        try {
            // Connect to the local WebSocket server
            const wsUrl = `ws://${window.location.host}/`;
            console.log('Connecting to WebSocket server:', wsUrl);

            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('Connected to WebSocket server');
                this.updateStatus('waiting', 'Ready for casting...');
            };

            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    console.log('Received WebSocket message:', message);
                    this.handleCastMessage(message);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.websocket.onclose = () => {
                console.log('WebSocket connection closed');
                this.updateStatus('error', 'Connection lost. Retrying...');
                // Attempt to reconnect after 3 seconds
                setTimeout(() => this.connectToWebSocketServer(), 3000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateStatus('error', 'Connection error');
            };

        } catch (error) {
            console.error('Error setting up WebSocket connection:', error);
            this.updateStatus('error', 'Failed to connect to server');
        }
    }

    startConnectionListener() {
        // Keep for backward compatibility
        window.castingReceiver = this;
    }

    handleCastMessage(message) {
        console.log('Received cast message:', message);
        
        switch (message.type) {
            case 'connect':
                this.handleConnect(message.clientInfo);
                break;
            case 'loadMedia':
                this.handleLoadMedia(message.data);
                break;
            case 'play':
                this.play();
                break;
            case 'pause':
                this.pause();
                break;
            case 'seek':
                this.seek(message.data.time);
                break;
            case 'volume':
                this.setVolume(message.data.volume);
                break;
            case 'subtitle':
                this.setSubtitle(message.data.index);
                break;
            case 'stop':
                this.stop();
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }

    handleConnect(clientInfo) {
        this.isConnected = true;
        this.updateStatus('connected', `Connected to ${clientInfo.name}`);
        this.sendMessage({ type: 'ready' });
    }

    handleLoadMedia(mediaData) {
        this.currentMedia = mediaData;
        this.subtitles = mediaData.subtitles || [];
        this.currentSubtitleIndex = -1;
        
        // Update media info
        this.mediaTitle.textContent = mediaData.title;
        this.mediaSubtitle.textContent = mediaData.subtitle;
        if (mediaData.poster) {
            this.mediaPosterImg.src = mediaData.poster;
        }
        
        // Load video
        this.videoSource.src = mediaData.mediaUrl;
        this.videoPlayer.load();
        
        // Switch to player screen
        this.showScreen('player');
        this.showMediaInfo();
    }

    play() {
        if (this.videoPlayer) {
            this.videoPlayer.play();
            this.sendMessage({ 
                type: 'playbackUpdate', 
                data: { 
                    isPlaying: true, 
                    currentTime: this.videoPlayer.currentTime 
                } 
            });
        }
    }

    pause() {
        if (this.videoPlayer) {
            this.videoPlayer.pause();
            this.sendMessage({ 
                type: 'playbackUpdate', 
                data: { 
                    isPlaying: false, 
                    currentTime: this.videoPlayer.currentTime 
                } 
            });
        }
    }

    seek(time) {
        if (this.videoPlayer) {
            this.videoPlayer.currentTime = time;
            this.sendMessage({ 
                type: 'playbackUpdate', 
                data: { 
                    currentTime: time 
                } 
            });
        }
    }

    setVolume(volume) {
        if (this.videoPlayer) {
            this.videoPlayer.volume = Math.max(0, Math.min(1, volume));
        }
    }

    setSubtitle(index) {
        this.currentSubtitleIndex = index;
        if (index >= 0 && index < this.subtitles.length) {
            // Enable subtitle track
            this.subtitleText.textContent = '';
            this.subtitleOverlay.style.display = 'block';
        } else {
            // Disable subtitles
            this.subtitleOverlay.style.display = 'none';
        }
    }

    stop() {
        if (this.videoPlayer) {
            this.videoPlayer.pause();
            this.videoPlayer.currentTime = 0;
        }
        this.showScreen('idle');
        this.currentMedia = null;
    }

    togglePlayPause() {
        if (this.videoPlayer.paused) {
            this.play();
        } else {
            this.pause();
        }
    }

    handleTimeUpdate() {
        if (this.isConnected) {
            this.sendMessage({ 
                type: 'playbackUpdate', 
                data: { 
                    currentTime: this.videoPlayer.currentTime,
                    duration: this.videoPlayer.duration,
                    isPlaying: !this.videoPlayer.paused
                } 
            });
        }
        
        // Update subtitle display
        this.updateSubtitleDisplay();
    }

    updateSubtitleDisplay() {
        if (this.currentSubtitleIndex >= 0 && this.subtitles.length > 0) {
            const currentTime = this.videoPlayer.currentTime;
            // Simple subtitle timing logic (would need proper VTT/SRT parsing in production)
            // For now, just show placeholder subtitle
            if (currentTime > 0) {
                this.subtitleText.textContent = 'Subtitle text would appear here';
            }
        }
    }

    handleVideoError(error) {
        console.error('Video error:', error);
        this.showError('Failed to load video. Please check the media URL and try again.');
        this.sendMessage({ 
            type: 'error', 
            data: { message: 'Video playback error' } 
        });
    }

    handleVideoEnded() {
        this.sendMessage({ 
            type: 'playbackUpdate', 
            data: { 
                isPlaying: false, 
                currentTime: this.videoPlayer.duration,
                ended: true
            } 
        });
    }

    showScreen(screenName) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        const targetScreen = document.getElementById(`${screenName}-screen`);
        if (targetScreen) {
            targetScreen.classList.add('active');
        }
    }

    showMediaInfo() {
        this.mediaInfo.classList.add('visible');
        
        if (this.mediaInfoTimeout) {
            clearTimeout(this.mediaInfoTimeout);
        }
        
        this.mediaInfoTimeout = setTimeout(() => {
            this.mediaInfo.classList.remove('visible');
        }, 3000);
    }

    showLoading() {
        this.loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }

    showError(message) {
        this.errorMessage.textContent = message;
        this.showScreen('error');
    }

    updateStatus(status, message) {
        this.statusIndicator.className = `status-dot ${status}`;
        this.statusText.textContent = message;
    }

    updatePlayPauseButton(isPlaying) {
        const playIcon = this.playPauseBtn.querySelector('.play-icon');
        const pauseIcon = this.playPauseBtn.querySelector('.pause-icon');
        
        if (isPlaying) {
            playIcon.style.display = 'none';
            pauseIcon.style.display = 'inline';
        } else {
            playIcon.style.display = 'inline';
            pauseIcon.style.display = 'none';
        }
    }

    retryConnection() {
        this.showScreen('idle');
        this.startWebSocketServer();
    }

    sendMessage(message) {
        console.log('Sending message:', message);

        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket not connected, cannot send message');
        }
    }
}

// Initialize the casting receiver when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new CastingReceiver();
});
