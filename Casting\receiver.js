// Netflix Clone Casting Receiver
class CastingReceiver {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.currentMedia = null;
        this.subtitles = [];
        this.currentSubtitleIndex = -1;
        this.mediaInfoTimeout = null;
        this.hls = null;
        this.subtitleTracks = [];
        this.currentSubtitleTrack = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.startWebSocketServer();
        this.updateDeviceInfo();
    }

    initializeElements() {
        // Screen elements
        this.idleScreen = document.getElementById('idle-screen');
        this.playerScreen = document.getElementById('player-screen');
        this.errorScreen = document.getElementById('error-screen');
        
        // Player elements
        this.videoPlayer = document.getElementById('video-player');
        this.videoSource = document.getElementById('video-source');
        this.subtitleOverlay = document.getElementById('subtitle-overlay');
        this.subtitleText = document.getElementById('subtitle-text');
        
        // Media info elements
        this.mediaInfo = document.getElementById('media-info');
        this.mediaPosterImg = document.getElementById('media-poster-img');
        this.mediaTitle = document.getElementById('media-title');
        this.mediaSubtitle = document.getElementById('media-subtitle');
        this.playPauseBtn = document.getElementById('play-pause-btn');
        
        // Status elements
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusText = document.getElementById('status-text');
        this.deviceName = document.getElementById('device-name');
        this.deviceIP = document.getElementById('device-ip');
        
        // Loading and error elements
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.clickToPlayOverlay = document.getElementById('click-to-play-overlay');
        this.errorMessage = document.getElementById('error-message');
        this.retryBtn = document.getElementById('retry-btn');
    }

    setupEventListeners() {
        // Video player events
        this.videoPlayer.addEventListener('loadstart', () => {
            console.log('Video loading started');
            this.showLoading();
        });
        this.videoPlayer.addEventListener('canplay', () => {
            console.log('Video can play');
            this.hideLoading();
        });
        this.videoPlayer.addEventListener('loadeddata', () => {
            console.log('Video data loaded');
            this.hideLoading();
        });
        this.videoPlayer.addEventListener('loadedmetadata', () => {
            console.log('Video metadata loaded, duration:', this.videoPlayer.duration);
            // Send initial duration to mobile app
            if (this.isConnected && this.videoPlayer.duration > 0) {
                this.sendMessage({
                    type: 'playbackUpdate',
                    data: {
                        currentTime: this.videoPlayer.currentTime || 0,
                        duration: this.videoPlayer.duration,
                        isPlaying: !this.videoPlayer.paused
                    }
                });
            }
        });
        this.videoPlayer.addEventListener('durationchange', () => {
            console.log('Duration changed:', this.videoPlayer.duration);
            // Send duration update to mobile app
            if (this.isConnected && this.videoPlayer.duration > 0) {
                this.sendMessage({
                    type: 'playbackUpdate',
                    data: {
                        currentTime: this.videoPlayer.currentTime || 0,
                        duration: this.videoPlayer.duration,
                        isPlaying: !this.videoPlayer.paused
                    }
                });
            }
        });
        this.videoPlayer.addEventListener('play', () => {
            console.log('Video playing');
            this.updatePlayPauseButton(true);
        });
        this.videoPlayer.addEventListener('pause', () => {
            console.log('Video paused');
            this.updatePlayPauseButton(false);
        });
        this.videoPlayer.addEventListener('timeupdate', () => this.handleTimeUpdate());
        this.videoPlayer.addEventListener('error', (e) => {
            console.error('Video error:', e);
            this.handleVideoError(e);
        });
        this.videoPlayer.addEventListener('ended', () => this.handleVideoEnded());
        
        // Control button events
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        this.retryBtn.addEventListener('click', () => this.retryConnection());
        
        // Mouse/touch events for media info and play interaction
        this.playerScreen.addEventListener('mousemove', () => this.showMediaInfo());
        this.playerScreen.addEventListener('click', () => {
            this.showMediaInfo();
            this.hideClickToPlayPrompt();
            // If video is paused and we have media loaded, try to play
            if (this.currentMedia && this.videoPlayer.paused) {
                this.play();
            }
        });
        this.playerScreen.addEventListener('touchstart', () => this.showMediaInfo());
    }

    updateDeviceInfo() {
        // Get device information
        const deviceName = `Casting Device (${window.location.hostname})`;
        const deviceIP = window.location.hostname;
        
        this.deviceName.textContent = deviceName;
        this.deviceIP.textContent = `IP: ${deviceIP}:8080`;
    }

    startWebSocketServer() {
        try {
            // For browser-based WebSocket server, we'll use a simple approach
            // In a real implementation, you'd need a proper WebSocket server
            this.setupWebSocketConnection();
        } catch (error) {
            console.error('Error starting WebSocket server:', error);
            this.updateStatus('error', 'Failed to start casting service');
        }
    }

    setupWebSocketConnection() {
        this.updateStatus('waiting', 'Waiting for connection...');
        this.connectToWebSocketServer();
    }

    connectToWebSocketServer() {
        try {
            // Connect to the local WebSocket server
            const wsUrl = `ws://${window.location.host}/`;
            console.log('Connecting to WebSocket server:', wsUrl);

            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('Connected to WebSocket server');
                this.updateStatus('waiting', 'Ready for casting...');
            };

            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    console.log('Received WebSocket message:', message);
                    this.handleCastMessage(message);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.websocket.onclose = () => {
                console.log('WebSocket connection closed');
                this.updateStatus('error', 'Connection lost. Retrying...');
                // Attempt to reconnect after 3 seconds
                setTimeout(() => this.connectToWebSocketServer(), 3000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateStatus('error', 'Connection error');
            };

        } catch (error) {
            console.error('Error setting up WebSocket connection:', error);
            this.updateStatus('error', 'Failed to connect to server');
        }
    }

    startConnectionListener() {
        // Keep for backward compatibility
        window.castingReceiver = this;
    }

    handleCastMessage(message) {
        console.log('Received cast message:', message);

        switch (message.type) {
            case 'connected':
                console.log('Connected to server:', message.message);
                this.updateStatus('connected', 'Connected to server');
                break;
            case 'connect':
                this.handleConnect(message.clientInfo);
                break;
            case 'loadMedia':
                this.handleLoadMedia(message.data);
                break;
            case 'play':
                this.play();
                break;
            case 'pause':
                this.pause();
                break;
            case 'seek':
                if (message.data && typeof message.data.time !== 'undefined') {
                    this.seek(message.data.time);
                }
                break;
            case 'volume':
                if (message.data && typeof message.data.volume !== 'undefined') {
                    this.setVolume(message.data.volume);
                }
                break;
            case 'subtitle':
                if (message.data && typeof message.data.index !== 'undefined') {
                    this.setSubtitle(message.data.index);
                }
                break;
            case 'stop':
                this.stop();
                break;
            case 'ready':
                console.log('Server is ready');
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }

    handleConnect(clientInfo) {
        this.isConnected = true;
        this.updateStatus('connected', `Connected to ${clientInfo?.name || 'Mobile Device'}`);
        console.log('Mobile device connected:', clientInfo);
        this.sendMessage({ type: 'ready' });
    }

    handleLoadMedia(mediaData) {
        console.log('Loading media:', mediaData.title);
        this.currentMedia = mediaData;
        this.subtitles = mediaData.subtitles || [];
        this.currentSubtitleIndex = -1;

        // Process and load subtitle tracks
        this.loadSubtitleTracks();

        // Update media info
        this.mediaTitle.textContent = mediaData.title;
        this.mediaSubtitle.textContent = mediaData.subtitle;
        if (mediaData.poster) {
            this.mediaPosterImg.src = mediaData.poster;
        }

        // Show loading
        this.showLoading();

        // Load video with proper error handling
        try {
            console.log('Loading video URL:', mediaData.mediaUrl);

            // Clean up previous HLS instance
            if (this.hls) {
                this.hls.destroy();
                this.hls = null;
            }

            // Check if it's an M3U8 stream
            if (mediaData.mediaUrl.includes('.m3u8')) {
                console.log('Loading HLS stream...');
                if (window.Hls && Hls.isSupported()) {
                    this.hls = new Hls({
                        enableWorker: true,
                        lowLatencyMode: false,
                        backBufferLength: 90
                    });

                    this.hls.loadSource(mediaData.mediaUrl);
                    this.hls.attachMedia(this.videoPlayer);

                    this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                        console.log('HLS manifest parsed, video ready');
                    });

                    this.hls.on(Hls.Events.ERROR, (event, data) => {
                        console.error('HLS error:', data);
                        if (data.fatal) {
                            this.showError(`HLS Error: ${data.details}`);
                        }
                    });
                } else if (this.videoPlayer.canPlayType('application/vnd.apple.mpegurl')) {
                    // Safari native HLS support
                    console.log('Using native HLS support');
                    this.videoSource.src = mediaData.mediaUrl;
                    this.videoSource.type = 'application/vnd.apple.mpegurl';
                    this.videoPlayer.load();
                } else {
                    throw new Error('HLS not supported in this browser');
                }
            } else {
                // Regular video file
                console.log('Loading regular video file...');
                this.videoSource.src = mediaData.mediaUrl;
                if (mediaData.mediaUrl.includes('.mp4')) {
                    this.videoSource.type = 'video/mp4';
                }
                this.videoPlayer.load();
            }

            this.videoPlayer.crossOrigin = 'anonymous';
            this.videoPlayer.preload = 'metadata';

            // Switch to player screen
            this.showScreen('player');
            this.showMediaInfo();

            // Update status
            this.updateStatus('connected', `Loading: ${mediaData.title}`);

            // Handle when video is ready to play
            this.videoPlayer.addEventListener('canplay', () => {
                console.log('Video ready to play');
                this.hideLoading();
                this.updateStatus('connected', `Ready: ${mediaData.title}`);
                // Don't auto-play, wait for play command from mobile app
                this.updatePlayPauseButton(false);

                // Log available text tracks
                console.log('Available text tracks:', this.videoPlayer.textTracks.length);
                for (let i = 0; i < this.videoPlayer.textTracks.length; i++) {
                    const track = this.videoPlayer.textTracks[i];
                    console.log(`Track ${i}:`, track.label, track.language, track.mode);
                }
            }, { once: true });

        } catch (error) {
            console.error('Error loading media:', error);
            this.showError(`Failed to load media: ${error.message}`);
        }
    }

    async loadSubtitleTracks() {
        console.log('Loading subtitle tracks:', this.subtitles);
        this.subtitleTracks = [];

        // Clear existing subtitle tracks from video element
        const existingTracks = this.videoPlayer.querySelectorAll('track');
        existingTracks.forEach(track => track.remove());

        if (this.subtitles && this.subtitles.length > 0) {
            for (let i = 0; i < this.subtitles.length; i++) {
                const subtitle = this.subtitles[i];
                try {
                    const track = await this.createSubtitleTrack(subtitle, i);
                    if (track) {
                        this.subtitleTracks.push(track);
                        this.videoPlayer.appendChild(track);
                    }
                } catch (error) {
                    console.error('Error loading subtitle track:', error);
                }
            }
            console.log(`Loaded ${this.subtitleTracks.length} subtitle tracks`);
        }
    }

    async createSubtitleTrack(subtitle, index) {
        if (!subtitle.url && !subtitle.file) {
            console.warn('Subtitle missing URL:', subtitle);
            return null;
        }

        const track = document.createElement('track');
        track.kind = 'subtitles';
        track.label = subtitle.label || subtitle.language || `Subtitle ${index + 1}`;
        track.srclang = subtitle.language || 'en';
        track.src = subtitle.url || subtitle.file;
        track.default = index === 0; // Make first subtitle default

        console.log('Created subtitle track:', track.label, track.src);
        return track;
    }

    play() {
        if (this.videoPlayer) {
            console.log('Attempting to play video...');
            const playPromise = this.videoPlayer.play();

            if (playPromise !== undefined) {
                playPromise
                    .then(() => {
                        console.log('Video playing successfully');
                        this.updateStatus('connected', `Playing: ${this.currentMedia?.title || 'Media'}`);
                        this.sendMessage({
                            type: 'playbackUpdate',
                            data: {
                                isPlaying: true,
                                currentTime: this.videoPlayer.currentTime,
                                duration: this.videoPlayer.duration || 0
                            }
                        });
                    })
                    .catch(error => {
                        console.error('Play failed:', error);
                        this.showClickToPlayPrompt();
                        this.sendMessage({
                            type: 'error',
                            data: { message: 'Play failed - user interaction required' }
                        });
                    });
            }
        }
    }

    pause() {
        if (this.videoPlayer) {
            console.log('Pausing video...');
            this.videoPlayer.pause();
            this.updateStatus('connected', `Paused: ${this.currentMedia?.title || 'Media'}`);
            this.sendMessage({
                type: 'playbackUpdate',
                data: {
                    isPlaying: false,
                    currentTime: this.videoPlayer.currentTime,
                    duration: this.videoPlayer.duration || 0
                }
            });
        }
    }

    seek(time) {
        if (this.videoPlayer && this.videoPlayer.duration > 0) {
            // Ensure time is within valid range
            const seekTime = Math.max(0, Math.min(time, this.videoPlayer.duration));
            console.log(`Seeking to: ${seekTime}s (requested: ${time}s, duration: ${this.videoPlayer.duration}s)`);

            this.videoPlayer.currentTime = seekTime;

            // Send immediate update
            this.sendMessage({
                type: 'playbackUpdate',
                data: {
                    currentTime: seekTime,
                    duration: this.videoPlayer.duration,
                    isPlaying: !this.videoPlayer.paused
                }
            });
        }
    }

    setVolume(volume) {
        if (this.videoPlayer) {
            this.videoPlayer.volume = Math.max(0, Math.min(1, volume));
        }
    }

    setSubtitle(index) {
        console.log('Setting subtitle index:', index);
        this.currentSubtitleIndex = index;

        // Disable all existing tracks
        const textTracks = this.videoPlayer.textTracks;
        for (let i = 0; i < textTracks.length; i++) {
            textTracks[i].mode = 'disabled';
        }

        if (index >= 0 && index < this.subtitleTracks.length) {
            // Enable the selected subtitle track
            const selectedTrack = textTracks[index];
            if (selectedTrack) {
                selectedTrack.mode = 'showing';
                console.log('Enabled subtitle track:', selectedTrack.label);

                // Also show in our custom overlay as fallback
                this.subtitleOverlay.style.display = 'block';
                this.currentSubtitleTrack = selectedTrack;

                // Listen for cue changes
                selectedTrack.addEventListener('cuechange', () => {
                    this.updateCustomSubtitleDisplay(selectedTrack);
                });
            }
        } else {
            // Disable subtitles
            this.subtitleOverlay.style.display = 'none';
            this.currentSubtitleTrack = null;
            this.subtitleText.textContent = '';
            console.log('Disabled all subtitles');
        }
    }

    stop() {
        if (this.videoPlayer) {
            this.videoPlayer.pause();
            this.videoPlayer.currentTime = 0;
        }
        this.showScreen('idle');
        this.currentMedia = null;
    }

    togglePlayPause() {
        if (this.videoPlayer.paused) {
            this.play();
        } else {
            this.pause();
        }
    }

    handleTimeUpdate() {
        if (this.isConnected && this.videoPlayer) {
            const currentTime = this.videoPlayer.currentTime || 0;
            const duration = this.videoPlayer.duration || 0;
            const isPlaying = !this.videoPlayer.paused;

            // Only send updates if we have valid duration
            if (duration > 0) {
                this.sendMessage({
                    type: 'playbackUpdate',
                    data: {
                        currentTime: currentTime,
                        duration: duration,
                        isPlaying: isPlaying,
                        buffered: this.getBufferedRanges()
                    }
                });
            }
        }

        // Update subtitle display
        this.updateSubtitleDisplay();
    }

    getBufferedRanges() {
        if (!this.videoPlayer || !this.videoPlayer.buffered) return [];

        const buffered = [];
        for (let i = 0; i < this.videoPlayer.buffered.length; i++) {
            buffered.push({
                start: this.videoPlayer.buffered.start(i),
                end: this.videoPlayer.buffered.end(i)
            });
        }
        return buffered;
    }

    updateSubtitleDisplay() {
        // This method is now mainly for fallback
        if (this.currentSubtitleTrack) {
            this.updateCustomSubtitleDisplay(this.currentSubtitleTrack);
        }
    }

    updateCustomSubtitleDisplay(track) {
        if (!track || !track.activeCues) {
            this.subtitleText.textContent = '';
            return;
        }

        // Get the current active cue
        const activeCues = track.activeCues;
        if (activeCues.length > 0) {
            // Display the text of the first active cue
            const cueText = activeCues[0].text;
            this.subtitleText.innerHTML = this.formatSubtitleText(cueText);
        } else {
            this.subtitleText.textContent = '';
        }
    }

    formatSubtitleText(text) {
        // Basic HTML formatting for subtitles
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>')
            .replace(/<c[^>]*>/g, '<span style="color: yellow;">')
            .replace(/<\/c>/g, '</span>')
            .replace(/<b>/g, '<strong>')
            .replace(/<\/b>/g, '</strong>')
            .replace(/<i>/g, '<em>')
            .replace(/<\/i>/g, '</em>');
    }

    handleVideoError(error) {
        console.error('Video error:', error);
        const videoError = this.videoPlayer.error;
        let errorMessage = 'Failed to load video. Please try again.';

        if (videoError) {
            switch (videoError.code) {
                case videoError.MEDIA_ERR_ABORTED:
                    errorMessage = 'Video playback was aborted.';
                    break;
                case videoError.MEDIA_ERR_NETWORK:
                    errorMessage = 'Network error occurred while loading video.';
                    break;
                case videoError.MEDIA_ERR_DECODE:
                    errorMessage = 'Video format not supported.';
                    break;
                case videoError.MEDIA_ERR_SRC_NOT_SUPPORTED:
                    errorMessage = 'Video source not supported or not found.';
                    break;
                default:
                    errorMessage = 'Unknown video error occurred.';
            }
        }

        this.showError(errorMessage);
        this.sendMessage({
            type: 'error',
            data: { message: errorMessage }
        });
    }

    handleVideoEnded() {
        this.sendMessage({ 
            type: 'playbackUpdate', 
            data: { 
                isPlaying: false, 
                currentTime: this.videoPlayer.duration,
                ended: true
            } 
        });
    }

    showScreen(screenName) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        const targetScreen = document.getElementById(`${screenName}-screen`);
        if (targetScreen) {
            targetScreen.classList.add('active');
        }
    }

    showMediaInfo() {
        this.mediaInfo.classList.add('visible');
        
        if (this.mediaInfoTimeout) {
            clearTimeout(this.mediaInfoTimeout);
        }
        
        this.mediaInfoTimeout = setTimeout(() => {
            this.mediaInfo.classList.remove('visible');
        }, 3000);
    }

    showLoading() {
        this.loadingOverlay.style.display = 'flex';
        this.clickToPlayOverlay.style.display = 'none';
    }

    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }

    showClickToPlayPrompt() {
        this.loadingOverlay.style.display = 'none';
        this.clickToPlayOverlay.style.display = 'flex';
    }

    hideClickToPlayPrompt() {
        this.clickToPlayOverlay.style.display = 'none';
    }

    showError(message) {
        this.errorMessage.textContent = message;
        this.showScreen('error');
    }

    updateStatus(status, message) {
        this.statusIndicator.className = `status-dot ${status}`;
        this.statusText.textContent = message;
    }

    updatePlayPauseButton(isPlaying) {
        const playIcon = this.playPauseBtn.querySelector('.play-icon');
        const pauseIcon = this.playPauseBtn.querySelector('.pause-icon');
        
        if (isPlaying) {
            playIcon.style.display = 'none';
            pauseIcon.style.display = 'inline';
        } else {
            playIcon.style.display = 'inline';
            pauseIcon.style.display = 'none';
        }
    }

    retryConnection() {
        this.showScreen('idle');
        this.startWebSocketServer();
    }

    sendMessage(message) {
        console.log('Sending message:', message);

        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket not connected, cannot send message');
        }
    }
}

// Initialize the casting receiver when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new CastingReceiver();
});
