# Netflix Clone - Casting Feature

This folder contains the standalone HTML casting receiver for the Netflix Clone React Native app.

## Files

- **receiver.html** - Main HTML page that acts as the casting receiver
- **receiver.js** - JavaScript logic for WebSocket communication and media playback
- **styles.css** - Styling for the casting receiver interface
- **README.md** - This documentation file

## Setup Instructions

### 1. Prerequisites

1. **Install Node.js:**
   - Download and install Node.js from https://nodejs.org/
   - Make sure `node` and `npm` are available in your system PATH

### 2. Setting up the Casting Server

1. **Navigate to the Casting folder:**
   ```bash
   cd Casting
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the casting server:**

   **Windows:**
   ```bash
   start-casting-server.bat
   ```

   **Mac/Linux:**
   ```bash
   chmod +x start-casting-server.sh
   ./start-casting-server.sh
   ```

   **Or manually:**
   ```bash
   node server.js
   ```

4. **Open the receiver interface:**
   - The server will show you the local IP address
   - Open `http://YOUR_IP_ADDRESS:8080/receiver.html` in a browser on your TV/computer
   - Example: `http://************:8080/receiver.html`

### 3. Network Requirements

- Ensure your TV/computer and mobile device are on the same WiFi network
- The server runs on port 8080 by default
- Make sure no firewall is blocking the connection
- Note the IP address shown in the server console

### 2. Using the Casting Feature

1. **From the Mobile App:**
   - Open any movie or TV show detail page
   - Tap the cast button (TV icon) next to the "My List" button
   - The app will scan for available casting devices

2. **Device Discovery:**
   - Select your casting device from the list
   - The app will connect and start casting

3. **Remote Control:**
   - Once connected, you'll see a remote control interface
   - Control playback, volume, and subtitles from your phone
   - The media plays on the TV/computer screen

## Features

### Casting Receiver (TV/Computer)
- **Idle Screen:** Shows device name and connection status
- **Media Player:** Full-screen video playback with subtitle support
- **Real-time Communication:** WebSocket-based communication with mobile app
- **Error Handling:** Displays error messages and retry options

### Mobile App Integration
- **Cast Button:** Available on movie and TV show detail pages
- **Device Discovery:** Automatic scanning for available receivers
- **Remote Control:** Full media control interface
- **Seamless Integration:** Works with existing TMDB API and streaming infrastructure

## Technical Details

### Communication Protocol
The casting system uses WebSocket communication with JSON messages:

```javascript
// Connection
{ type: 'connect', clientInfo: { name: 'Netflix Clone App', version: '1.0.0' } }

// Load Media
{ 
  type: 'loadMedia', 
  data: { 
    mediaUrl: 'stream_url',
    title: 'Movie Title',
    subtitle: 'Season 1, Episode 1',
    poster: 'poster_url',
    subtitles: [...],
    mediaType: 'movie'
  } 
}

// Playback Controls
{ type: 'play' }
{ type: 'pause' }
{ type: 'seek', data: { time: 120 } }
{ type: 'volume', data: { volume: 0.8 } }
{ type: 'subtitle', data: { index: 0 } }
{ type: 'stop' }
```

### Device Discovery
- Scans common local network IP ranges (192.168.x.x, 10.0.x.x, 172.16.x.x)
- Looks for devices responding on port 8080
- Uses HTTP ping endpoint for device identification

## Troubleshooting

### Common Issues

1. **No devices found:**
   - **Make sure the casting server is running** (most common issue)
   - Run `node server.js` in the Casting folder
   - Ensure both devices are on the same WiFi network
   - Check that the receiver page is open: `http://YOUR_IP:8080/receiver.html`
   - Verify no firewall is blocking port 8080
   - Try the IP address shown in the server console

2. **"Connection lost" or "Connection error":**
   - Restart the casting server
   - Refresh the receiver page in your browser
   - Check the server console for error messages
   - Try scanning for devices again

3. **Server won't start:**
   - Make sure Node.js is installed: `node --version`
   - Install dependencies: `npm install`
   - Check if port 8080 is already in use
   - Try running on a different port: `PORT=8081 node server.js`

4. **Media won't play:**
   - Check the server console for error messages
   - Verify the streaming URL is accessible
   - Check browser console for errors
   - Ensure media format is supported by the browser

### Browser Compatibility
- **Recommended:** Chrome, Firefox, Safari, Edge
- **Requirements:** WebSocket support, HTML5 video
- **Note:** Some smart TV browsers may have limited functionality

## Development Notes

### For Developers
- The receiver uses a simplified WebSocket approach for demonstration
- In production, consider using a proper WebSocket server (Node.js, Python, etc.)
- The device discovery mechanism can be enhanced with mDNS/Bonjour
- Subtitle support can be improved with proper VTT/SRT parsing

### Extending the Feature
- Add quality selection controls
- Implement playlist/queue functionality
- Add support for multiple simultaneous connections
- Enhance subtitle rendering with styling options

## Security Considerations

- The current implementation is for local network use only
- No authentication is implemented (suitable for home networks)
- For public deployment, add proper authentication and encryption
- Consider implementing device pairing for enhanced security

---

**Note:** This is a demonstration implementation of casting functionality. For production use, consider using established protocols like Google Cast SDK or AirPlay.
