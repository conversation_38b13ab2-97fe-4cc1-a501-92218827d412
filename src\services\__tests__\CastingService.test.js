// CastingService Test
// This is a basic test file to verify the CastingService functionality

import castingService from '../CastingService';

describe('CastingService', () => {
  beforeEach(() => {
    // Reset the service state before each test
    castingService.disconnect();
    castingService.discoveredDevices = [];
    castingService.isScanning = false;
  });

  test('should initialize with correct default state', () => {
    expect(castingService.isConnected()).toBe(false);
    expect(castingService.getConnectedDevice()).toBe(null);
    expect(castingService.getDiscoveredDevices()).toEqual([]);
  });

  test('should handle event listeners correctly', () => {
    const mockCallback = jest.fn();
    
    // Add event listener
    castingService.addEventListener('test', mockCallback);
    
    // Emit event
    castingService.emit('test', { data: 'test' });
    
    expect(mockCallback).toHaveBeenCalledWith({ data: 'test' });
    
    // Remove event listener
    castingService.removeEventListener('test', mockCallback);
    
    // Emit event again
    castingService.emit('test', { data: 'test2' });
    
    // Should not be called again
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });

  test('should generate correct local network IPs', async () => {
    const ips = await castingService.getLocalNetworkIPs();
    
    expect(ips).toContain('***********');
    expect(ips).toContain('***********');
    expect(ips).toContain('********');
    expect(ips).toContain('**********');
    
    // Should have IPs for all ranges
    expect(ips.length).toBeGreaterThan(1000);
  });

  test('should handle message sending when not connected', () => {
    const result = castingService.sendMessage({ type: 'test' });
    expect(result).toBe(false);
  });

  test('should stop scanning correctly', () => {
    castingService.isScanning = true;
    castingService.stopScanning();
    expect(castingService.isScanning).toBe(false);
  });

  test('should handle media casting parameters', async () => {
    const mediaInfo = {
      streamUrl: 'https://example.com/video.mp4',
      title: 'Test Movie',
      subtitle: 'Test Subtitle',
      poster: 'https://example.com/poster.jpg',
      subtitles: [],
      mediaType: 'movie'
    };

    // This should not throw an error even when not connected
    expect(() => {
      castingService.castMedia(mediaInfo);
    }).toThrow('No device connected');
  });
});

// Mock WebSocket for testing
global.WebSocket = jest.fn(() => ({
  send: jest.fn(),
  close: jest.fn(),
  readyState: 1, // OPEN
  addEventListener: jest.fn(),
  removeEventListener: jest.fn()
}));

// Mock fetch for testing
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({
      name: 'Test Device',
      type: 'netflix-clone-receiver'
    })
  })
);
