<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casting Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0F1014;
            color: white;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #5E38F4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4a2bc2;
        }
        .log {
            background: #000;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: rgba(0, 255, 0, 0.2); }
        .status.error { background: rgba(255, 0, 0, 0.2); }
        .status.info { background: rgba(0, 0, 255, 0.2); }
    </style>
</head>
<body>
    <div class="container">
        <h1>Netflix Clone Casting Test</h1>
        
        <div class="test-section">
            <h2>Server Status</h2>
            <div id="server-status" class="status info">Checking server...</div>
            <button onclick="checkServer()">Check Server</button>
        </div>

        <div class="test-section">
            <h2>WebSocket Connection</h2>
            <div id="ws-status" class="status info">Not connected</div>
            <button onclick="connectWebSocket()">Connect WebSocket</button>
            <button onclick="disconnectWebSocket()">Disconnect</button>
        </div>

        <div class="test-section">
            <h2>Test Media Casting</h2>
            <div id="cast-status" class="status info">Ready to test</div>
            <button onclick="testCasting()">Test Cast Media</button>
            <button onclick="testPlayPause()">Test Play/Pause</button>
            <button onclick="testSeek()">Test Seek</button>
        </div>

        <div class="test-section">
            <h2>Debug Log</h2>
            <button onclick="clearLog()">Clear Log</button>
            <div id="debug-log" class="log"></div>
        </div>
    </div>

    <script>
        let ws = null;
        
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        async function checkServer() {
            try {
                log('Checking server status...');
                const response = await fetch('/ping');
                const data = await response.json();
                log(`Server response: ${JSON.stringify(data)}`);
                updateStatus('server-status', `Server OK: ${data.name}`, 'success');
            } catch (error) {
                log(`Server check failed: ${error.message}`);
                updateStatus('server-status', `Server Error: ${error.message}`, 'error');
            }
        }

        function connectWebSocket() {
            try {
                log('Connecting to WebSocket...');
                ws = new WebSocket(`ws://${window.location.host}/`);
                
                ws.onopen = () => {
                    log('WebSocket connected');
                    updateStatus('ws-status', 'Connected', 'success');
                };
                
                ws.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    log(`Received: ${JSON.stringify(message)}`);
                };
                
                ws.onclose = () => {
                    log('WebSocket disconnected');
                    updateStatus('ws-status', 'Disconnected', 'error');
                };
                
                ws.onerror = (error) => {
                    log(`WebSocket error: ${error}`);
                    updateStatus('ws-status', 'Error', 'error');
                };
                
            } catch (error) {
                log(`WebSocket connection failed: ${error.message}`);
                updateStatus('ws-status', `Error: ${error.message}`, 'error');
            }
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                log('WebSocket disconnected manually');
                updateStatus('ws-status', 'Disconnected', 'info');
            }
        }

        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                log(`Sent: ${JSON.stringify(message)}`);
                return true;
            } else {
                log('WebSocket not connected');
                updateStatus('cast-status', 'WebSocket not connected', 'error');
                return false;
            }
        }

        function testCasting() {
            const testMedia = {
                type: 'loadMedia',
                data: {
                    mediaUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                    title: 'Test Video - Big Buck Bunny',
                    subtitle: 'Test Movie',
                    poster: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/Big_buck_bunny_poster_big.jpg/220px-Big_buck_bunny_poster_big.jpg',
                    subtitles: [],
                    mediaType: 'movie'
                }
            };
            
            if (sendMessage(testMedia)) {
                updateStatus('cast-status', 'Test media sent', 'success');
            }
        }

        function testPlayPause() {
            if (sendMessage({ type: 'play' })) {
                updateStatus('cast-status', 'Play command sent', 'success');
                setTimeout(() => {
                    if (sendMessage({ type: 'pause' })) {
                        updateStatus('cast-status', 'Pause command sent', 'success');
                    }
                }, 3000);
            }
        }

        function testSeek() {
            if (sendMessage({ type: 'seek', data: { time: 30 } })) {
                updateStatus('cast-status', 'Seek command sent', 'success');
            }
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // Auto-check server on load
        window.addEventListener('load', () => {
            checkServer();
        });
    </script>
</body>
</html>
