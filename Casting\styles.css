/* Netflix Clone Casting Receiver Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0F1014 0%, #1a1a2e 50%, #16213e 100%);
    color: #E1E1E1;
    overflow: hidden;
    height: 100vh;
}

#app {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
}

.screen.active {
    display: flex;
}

/* Idle Screen */
#idle-screen {
    flex-direction: column;
    text-align: center;
    padding: 40px;
}

.idle-content {
    max-width: 600px;
    width: 100%;
}

.logo {
    margin-bottom: 60px;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: #5E38F4;
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    font-weight: bold;
    margin: 0 auto 20px;
}

.logo h1 {
    font-size: 48px;
    font-weight: bold;
    color: #5E38F4;
    margin: 0;
}

.status-info {
    margin-bottom: 40px;
}

.device-info h2 {
    font-size: 32px;
    margin-bottom: 10px;
    color: #FFFFFF;
}

.device-info p {
    font-size: 18px;
    color: rgba(235, 235, 245, 0.6);
}

.connection-status {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
    gap: 12px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.waiting {
    background: #FFA500;
}

.status-dot.connected {
    background: #00FF00;
    animation: none;
}

.status-dot.error {
    background: #FF4444;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

#status-text {
    font-size: 16px;
    color: rgba(235, 235, 245, 0.8);
}

.instructions {
    margin-top: 40px;
}

.instructions p {
    font-size: 16px;
    color: rgba(235, 235, 245, 0.6);
    margin-bottom: 8px;
}

/* Player Screen */
#player-screen {
    background: #000;
}

#video-player {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Subtitle Overlay */
.subtitle-overlay {
    position: absolute;
    bottom: 80px;
    left: 0;
    right: 0;
    text-align: center;
    pointer-events: none;
    z-index: 10;
}

.subtitle-text {
    display: inline-block;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 24px;
    line-height: 1.4;
    max-width: 80%;
    word-wrap: break-word;
}

/* Media Info Overlay */
.media-info {
    position: absolute;
    top: 40px;
    left: 40px;
    right: 40px;
    display: flex;
    align-items: center;
    background: linear-gradient(90deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 70%, transparent 100%);
    padding: 20px;
    border-radius: 12px;
    z-index: 5;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-info.visible {
    opacity: 1;
}

.media-poster {
    width: 80px;
    height: 120px;
    margin-right: 20px;
    border-radius: 8px;
    overflow: hidden;
}

.media-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-details {
    flex: 1;
}

.media-details h2 {
    font-size: 28px;
    margin-bottom: 8px;
    color: #FFFFFF;
}

.media-details p {
    font-size: 16px;
    color: rgba(235, 235, 245, 0.8);
}

.playback-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: background 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 20;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #5E38F4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    font-size: 18px;
    color: rgba(235, 235, 245, 0.8);
}

/* Click to Play Overlay */
.play-prompt {
    text-align: center;
}

.play-icon-large {
    font-size: 80px;
    color: #5E38F4;
    margin-bottom: 20px;
    text-shadow: 0 0 20px rgba(94, 56, 244, 0.5);
}

.play-prompt p {
    font-size: 24px;
    color: #FFFFFF;
    margin-bottom: 10px;
}

.play-prompt small {
    font-size: 14px;
    color: rgba(235, 235, 245, 0.6);
}

/* Error Screen */
#error-screen {
    flex-direction: column;
    text-align: center;
    padding: 40px;
}

.error-content {
    max-width: 500px;
}

.error-icon {
    font-size: 80px;
    color: #FF4444;
    margin-bottom: 30px;
}

.error-content h2 {
    font-size: 36px;
    margin-bottom: 20px;
    color: #FFFFFF;
}

.error-content p {
    font-size: 18px;
    color: rgba(235, 235, 245, 0.6);
    margin-bottom: 40px;
    line-height: 1.5;
}

.retry-button {
    background: #5E38F4;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
}

.retry-button:hover {
    background: #4a2bc2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .logo h1 {
        font-size: 36px;
    }
    
    .device-info h2 {
        font-size: 24px;
    }
    
    .media-info {
        top: 20px;
        left: 20px;
        right: 20px;
        padding: 15px;
    }
    
    .media-details h2 {
        font-size: 20px;
    }
    
    .subtitle-text {
        font-size: 18px;
    }
}
