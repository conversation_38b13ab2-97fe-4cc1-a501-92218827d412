import React, { memo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SLOTH_COLORS } from '../styles/sloth';

const { width } = Dimensions.get('window');

const GlassBottomNav = memo(({ 
  // CHANGED: We now correctly accept the `sections` prop passed by the parent.
  // We default to an empty array to prevent any possibility of a .map error.
  sections = [],
  activeSection = 'home',
  onSectionChange,
  providerTheme,
}) => {
  const insets = useSafeAreaInsets();

  const handleSectionPress = (sectionId) => {
    if (onSectionChange) {
      onSectionChange(sectionId);
    }
  };

  const getIconColor = (sectionId) => {
    return activeSection === sectionId 
      ? (providerTheme?.primary || SLOTH_COLORS.primary)
      : SLOTH_COLORS.textSecondary;
  };

  // Simplified text color logic for better consistency
  const getTextColor = (sectionId) => {
    return activeSection === sectionId
      ? getIconColor(sectionId) // Active text matches active icon color
      : SLOTH_COLORS.textSecondary;
  };

  return (
    <View style={[
      // Reverted positioning to a simpler, more stable bottom dock
      styles.container,
      { paddingBottom: insets.bottom } // Respect the safe area at the bottom
    ]}>
      <BlurView
        intensity={90}
        tint="dark"
        style={styles.blurContainer}
      >
        <View style={styles.navContent}>
          {/* 
            CHANGED: We now map over the 'sections' prop from the parent.
            This is the main fix for the crash.
          */}
          {sections.map((section) => {
            const isActive = activeSection === section.id;
            
            return (
              <TouchableOpacity
                // CHANGED: Use `section.id` from our dynamic object for the key.
                key={section.id}
                style={styles.navItem}
                onPress={() => handleSectionPress(section.id)}
                activeOpacity={0.7}
              >
                <Ionicons
                  // CHANGED: Use a common pattern for active/inactive icons.
                  name={isActive ? section.icon : `${section.icon}-outline`}
                  size={isActive ? 24 : 22}
                  color={getIconColor(section.id)}
                />
                <Text 
                  style={[ styles.navText, { color: getTextColor(section.id) }]}
                  numberOfLines={1}
                >
                  {/* CHANGED: Use `section.label` from our dynamic object. */}
                  {section.label}
                </Text>
              </TouchableOpacity>
            )
          })}
        </View>
      </BlurView>
    </View>
  );
});

// Styles were also slightly simplified for stability
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    // The BlurView itself will have padding, not the outer container
  },
  blurContainer: {
    // The entire component is the blur view now
  },
  navContent: {
    flexDirection: 'row',
    height: 70, // A standard, safe height for a nav bar
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  navItem: {
    flex: 1,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  navText: {
    fontSize: 11,
    fontWeight: '600',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default GlassBottomNav;