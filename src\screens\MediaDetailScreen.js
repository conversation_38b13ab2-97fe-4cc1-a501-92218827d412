import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  ImageBackground,
  Image, // Already imported, good to go
  ActivityIndicator,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';
import WatchHistoryService from '../services/WatchHistoryService';
import MyListService, { MY_LIST_CATEGORIES, CATEGORY_LABELS } from '../services/MyListService';
import AnalyticsService from '../services/AnalyticsService';
import MediaRow from '../components/MediaRow';
import CastRow from '../components/CastRow';
import VideoRow from '../components/VideoRow';
import castingService from '../services/CastingService';

// Enhanced MediaDetailHeader with resume functionality
const MediaDetailHeader = ({ item, mediaType, navigation }) => {
  const [watchProgress, setWatchProgress] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isInMyList, setIsInMyList] = useState(false);
  const [myListCategory, setMyListCategory] = useState(null);
  const [isCasting, setIsCasting] = useState(false);
  const [connectedDevice, setConnectedDevice] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Track content view analytics
        await AnalyticsService.trackContentView(item, mediaType, 'detail_screen');

        const [progress, inMyList, category] = await Promise.all([
          WatchHistoryService.getProgress(item, mediaType),
          MyListService.isInMyList(item, mediaType),
          MyListService.getItemCategory(item, mediaType)
        ]);
        setWatchProgress(progress);
        setIsInMyList(inMyList);
        setMyListCategory(category);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Check casting status
    const connectedDevice = castingService.getConnectedDevice();
    if (connectedDevice) {
      setIsCasting(true);
      setConnectedDevice(connectedDevice);
    }

    // Listen for casting events
    const handleConnected = (device) => {
      setIsCasting(true);
      setConnectedDevice(device);
    };

    const handleDisconnected = () => {
      setIsCasting(false);
      setConnectedDevice(null);
    };

    castingService.addEventListener('connected', handleConnected);
    castingService.addEventListener('disconnected', handleDisconnected);

    return () => {
      castingService.removeEventListener('connected', handleConnected);
      castingService.removeEventListener('disconnected', handleDisconnected);
    };
  }, [item.id, mediaType]);

  const handlePlayPress = () => {
    if (mediaType === 'tv') {
        navigation.navigate('TVEpisodes', { item: item });
    } else {
        navigation.navigate('Player', {
            item: item,
            mediaType: 'movie'
        });
    }
  };

  const handleResumePress = () => {
    if (mediaType === 'tv') {
        navigation.navigate('TVEpisodes', { item: item });
    } else {
        navigation.navigate('Player', {
            item: item,
            mediaType: 'movie'
        });
    }
  };

  const formatProgressText = () => {
    if (!watchProgress) return '';
    const percent = Math.round(watchProgress.progressPercent * 100);
    return `${percent}% watched`;
  };

  const handleMyListPress = () => {
    if (isInMyList) {
      // Show category selection or remove
      Alert.alert(
        'My List',
        `"${item.title || item.name}" is in your ${CATEGORY_LABELS[myListCategory]} list`,
        [
          {
            text: 'Change Category',
            onPress: showCategorySelector,
          },
          {
            text: 'Remove from List',
            style: 'destructive',
            onPress: handleRemoveFromMyList,
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]
      );
    } else {
      showCategorySelector();
    }
  };

  const showCategorySelector = () => {
    const categories = Object.entries(MY_LIST_CATEGORIES).map(([key, value]) => ({
      text: CATEGORY_LABELS[value],
      onPress: () => handleAddToMyList(value),
    }));

    Alert.alert(
      'Add to My List',
      'Choose a category:',
      [
        ...categories,
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleAddToMyList = async (category) => {
    try {
      await MyListService.addToMyList(item, mediaType, category);
      await AnalyticsService.trackMyListAction('add', item, mediaType, category);
      setIsInMyList(true);
      setMyListCategory(category);
      Alert.alert('Success', `Added to ${CATEGORY_LABELS[category]}`);
    } catch (error) {
      console.error('Error adding to my list:', error);
      Alert.alert('Error', 'Failed to add to My List');
    }
  };

  const handleRemoveFromMyList = async () => {
    try {
      await MyListService.removeFromMyList(item, mediaType);
      await AnalyticsService.trackMyListAction('remove', item, mediaType, myListCategory);
      setIsInMyList(false);
      setMyListCategory(null);
      Alert.alert('Success', 'Removed from My List');
    } catch (error) {
      console.error('Error removing from my list:', error);
      Alert.alert('Error', 'Failed to remove from My List');
    }
  };

  const handleCastPress = () => {
    if (isCasting) {
      // If already casting, navigate to remote control
      navigation.navigate('RemoteControl', {
        item,
        mediaType,
        connectedDevice
      });
    } else {
      // If not casting, open device discovery
      navigation.navigate('DeviceDiscovery', {
        item,
        mediaType
      });
    }
  };

  const shouldShowResume = watchProgress && watchProgress.progressPercent > 0.05 && watchProgress.progressPercent < 0.9;
  const shouldShowWatchAgain = watchProgress && watchProgress.progressPercent >= 0.9;
  const playButtonLabel = shouldShowWatchAgain ? 'Watch Again' : (mediaType === 'tv' ? 'View Seasons' : 'Play');

  return (
    <View>
      {/* HEADER: Contains backdrop */}
      <View>
        <ImageBackground
          source={{ uri: tmdbApi.getBackdropUrl(item.backdrop_path) }}
          style={slothStyles.detailBackdrop}
        >
          <LinearGradient
            colors={['rgba(15,16,20,0.1)', SLOTH_COLORS.background]}
            style={{ flex: 1 }}
          />
        </ImageBackground>
      </View>

      {/* MAIN CONTENT Area (everything except "More Like This") */}
      <View style={slothStyles.detailContent}>
        {/* Poster + Title Block */}
        <View style={slothStyles.detailPosterInfoBlock}>
        
          <View style={slothStyles.detailTitleBlock}>
            {/* --- MODIFICATION START --- */}
            <Image
              source={{
                uri: `https://sorastream-five.vercel.app/logo/${mediaType}/${item.id}`
              }}
              style={slothStyles.detailLogo}
              resizeMode="contain"
            />
            {/* --- MODIFICATION END --- */}
            <View style={slothStyles.detailMetaRow}>
              {item.vote_average > 0 && (
                <>
                  <Ionicons name="star" size={14} style={slothStyles.detailStarIcon}/>
                  <Text style={slothStyles.detailMetaText}>{item.vote_average.toFixed(1)}</Text>
                </>
              )}
              <Text style={slothStyles.detailMetaText}>
                {new Date(item.release_date || item.first_air_date).getFullYear()}
              </Text>
            </View>
          </View>
        </View>

        {/* Progress Info */}
        {watchProgress && watchProgress.progressPercent > 0.05 && (
          <View style={slothStyles.detailProgressInfo}>
            <View style={slothStyles.detailProgressBar}>
              <View
                style={[
                  slothStyles.detailProgressFill,
                  { width: `${Math.min(watchProgress.progressPercent * 100, 100)}%` }
                ]}
              />
            </View>
            <Text style={slothStyles.detailProgressText}>
              {formatProgressText()}
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={slothStyles.detailMainActions}>
          {shouldShowResume && (
            <TouchableOpacity style={slothStyles.detailResumeButton} onPress={handleResumePress}>
              <Ionicons name="play" size={20} color={SLOTH_COLORS.background} />
              <Text style={slothStyles.detailResumeButtonText}>
                {mediaType === 'tv' && watchProgress.episode
                  ? `Resume S${watchProgress.season?.season_number}:E${watchProgress.episode?.episode_number}`
                  : 'Resume'
                }
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              slothStyles.detailPlayButton,
              shouldShowResume && { flex: 0.8 }
            ]}
            onPress={handlePlayPress}
          >
            <Ionicons name="play" size={20} color={SLOTH_COLORS.white} />
            <Text style={slothStyles.detailPlayButtonText}>{playButtonLabel}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={slothStyles.detailIconBtn} onPress={handleMyListPress}>
            <Ionicons
              name={isInMyList ? "checkmark" : "add"}
              size={26}
              color={isInMyList ? SLOTH_COLORS.primary : SLOTH_COLORS.white}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={isCasting ? slothStyles.detailCastBtnActive : slothStyles.detailCastBtn}
            onPress={handleCastPress}
          >
            <Ionicons
              name={isCasting ? "tv" : "tv-outline"}
              size={26}
              color={isCasting ? SLOTH_COLORS.white : SLOTH_COLORS.white}
            />
          </TouchableOpacity>
        </View>

        {/* Synopsis */}
        <View>
          <Text style={slothStyles.detailSectionTitle}>Synopsis</Text>
          <Text style={slothStyles.detailOverview}>{item.overview}</Text>
        </View>
      </View>
    </View>
  );
};

const MediaDetailScreen = ({ route, navigation }) => {
  const { item: initialItem, mediaType } = route.params;
  const [item, setItem] = useState(initialItem);
  const [loading, setLoading] = useState(true);
  const [relatedContent, setRelatedContent] = useState([]);
  const [castData, setCastData] = useState([]);
  const [videoData, setVideoData] = useState([]);
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef(null);

  useEffect(() => {
    scrollViewRef.current?.scrollTo({ y: 0, animated: false });
    loadAllData();

    // Completely disable the back gesture to test if it's causing scrolling issues
    navigation.setOptions({
      gestureEnabled: false,
    });
  }, [initialItem.id, navigation]);

  const loadAllData = async () => {
    try {
      setLoading(true);
      const detailData = mediaType === 'tv'
        ? await tmdbApi.getTVDetails(initialItem.id)
        : await tmdbApi.getMovieDetails(initialItem.id);

      setItem(detailData);

      // Process related content
      const recommendations = detailData.recommendations?.results || [];
      const similar = detailData.similar?.results || [];
      const combined = [...recommendations, ...similar];
      const uniqueContent = Array.from(new Map(combined.map(c => [c.id, c])).values())
        .filter(c => c.poster_path);
      setRelatedContent(uniqueContent);

      // Process cast data
      const cast = detailData.credits?.cast || [];
      const filteredCast = cast
        .filter(member => member.profile_path && member.name && member.character)
        .slice(0, 20); // Limit to first 20 cast members
      setCastData(filteredCast);

      // Process video data
      const videos = detailData.videos?.results || [];
      const filteredVideos = videos
        .filter(video =>
          video.site === 'YouTube' &&
          (video.type === 'Trailer' || video.type === 'Teaser' || video.type === 'Clip') &&
          video.key
        )
        .slice(0, 10); // Limit to first 10 videos
      setVideoData(filteredVideos);

    } catch (error) {
      console.error("Error loading full detail screen:", error);
      Alert.alert("Error", "Could not load all details. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const handleItemPress = (selectedItem) => {
    const nextMediaType = selectedItem.media_type || (selectedItem.first_air_date ? 'tv' : 'movie');
    navigation.push('MediaDetail', { item: selectedItem, mediaType: nextMediaType });
  };

  const handleCastPress = (castMember) => {
    navigation.navigate('CastDetail', { castMember });
  };
  
  if (loading) {
    return <View style={slothStyles.loadingContainer}><ActivityIndicator size="large" color={SLOTH_COLORS.primary} /></View>;
  }

  return (
    <View style={slothStyles.container}>
      <StatusBar barStyle="light-content" />

      {/* Main scrollable content */}
      <ScrollView
        ref={scrollViewRef}
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingBottom: 40 }}
      >
        <MediaDetailHeader
          item={item}
          mediaType={mediaType}
          navigation={navigation}
        />

        {/* Cast section */}
        {castData.length > 0 && (
          <CastRow
            title="Cast"
            data={castData}
            loading={loading}
            onCastPress={handleCastPress}
          />
        )}

        {/* Trailers & Videos section */}
        {videoData.length > 0 && (
          <VideoRow
            title="Trailers & Videos"
            data={videoData}
            loading={loading}
          />
        )}

        {/* More Like This section */}
        {relatedContent.length > 0 && (
          <View>
            <MediaRow
              title="More Like This"
              data={relatedContent}
              onItemPress={handleItemPress}
            />
          </View>
        )}
      </ScrollView>

      {/* Back button overlay */}
      <View style={[slothStyles.detailHeader, { top: insets.top }]}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={slothStyles.iconContainer}
        >
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default MediaDetailScreen;