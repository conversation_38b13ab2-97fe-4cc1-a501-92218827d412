// CastingService - handles WebSocket communication with casting devices

class CastingService {
  constructor() {
    this.connectedDevice = null;
    this.websocket = null;
    this.discoveredDevices = [];
    this.isScanning = false;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 3;
    this.scanTimeout = null;
  }

  // Event listener management
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => callback(data));
    }
  }

  // Device discovery
  async scanForDevices() {
    if (this.isScanning) return this.discoveredDevices;
    
    this.isScanning = true;
    this.discoveredDevices = [];
    this.emit('scanStarted');

    try {
      // Scan common local network IP ranges
      const localIPs = await this.getLocalNetworkIPs();
      const scanPromises = localIPs.map(ip => this.checkDeviceAtIP(ip));
      
      // Set timeout for scanning
      this.scanTimeout = setTimeout(() => {
        this.isScanning = false;
        this.emit('scanCompleted', this.discoveredDevices);
      }, 10000); // 10 second scan timeout

      await Promise.allSettled(scanPromises);
      
      if (this.scanTimeout) {
        clearTimeout(this.scanTimeout);
        this.scanTimeout = null;
      }
      
      this.isScanning = false;
      this.emit('scanCompleted', this.discoveredDevices);
      
      return this.discoveredDevices;
    } catch (error) {
      console.error('Error scanning for devices:', error);
      this.isScanning = false;
      this.emit('scanError', error);
      return [];
    }
  }

  async getLocalNetworkIPs() {
    // Generate common local network IP ranges
    const baseIPs = ['192.168.1', '192.168.0', '10.0.0', '172.16.0'];
    const ips = [];
    
    baseIPs.forEach(base => {
      for (let i = 1; i <= 254; i++) {
        ips.push(`${base}.${i}`);
      }
    });
    
    return ips;
  }

  async checkDeviceAtIP(ip) {
    try {
      // Try to connect to potential casting receiver on port 8080
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000);

      const response = await fetch(`http://${ip}:8080/ping`, {
        method: 'GET',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const deviceInfo = await response.json();
        const device = {
          id: `${ip}:8080`,
          name: deviceInfo.name || `Casting Device (${ip})`,
          ip: ip,
          port: 8080,
          status: 'available',
          type: 'netflix-clone-receiver'
        };

        // Check if device already exists to avoid duplicates
        const existingIndex = this.discoveredDevices.findIndex(d => d.id === device.id);
        if (existingIndex === -1) {
          this.discoveredDevices.push(device);
          this.emit('deviceFound', device);
        }
      }
    } catch (error) {
      // Device not available at this IP, continue scanning
      // This is expected for most IPs, so we don't log it
    }
  }

  // Connection management
  async connectToDevice(device) {
    if (this.websocket) {
      this.disconnect();
    }

    try {
      this.connectedDevice = device;
      this.websocket = new WebSocket(`ws://${device.ip}:${device.port}`);

      this.websocket.onopen = () => {
        console.log('Connected to casting device:', device.name);
        this.reconnectAttempts = 0;
        this.emit('connected', device);

        // Send initial connection message
        this.sendMessage({
          type: 'connect',
          clientInfo: {
            name: 'Netflix Clone App',
            version: '1.0.0'
          }
        });
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing message:', error);
        }
      };

      this.websocket.onclose = () => {
        console.log('Disconnected from casting device');
        this.emit('disconnected');
        this.attemptReconnect();
      };

      this.websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.emit('error', error);
      };

    } catch (error) {
      console.error('Error connecting to device:', error);
      this.emit('connectionError', error);
      throw error;
    }
  }

  disconnect() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    this.connectedDevice = null;
    this.reconnectAttempts = 0;
  }

  async attemptReconnect() {
    if (!this.connectedDevice || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    this.reconnectAttempts++;
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connectToDevice(this.connectedDevice);
    }, 2000 * this.reconnectAttempts); // Exponential backoff
  }

  // Message handling
  handleMessage(message) {
    switch (message.type) {
      case 'connected':
        console.log('Receiver connected:', message.message);
        break;
      case 'status':
        this.emit('statusUpdate', message.data);
        break;
      case 'playbackUpdate':
        this.emit('playbackUpdate', message.data);
        break;
      case 'error':
        this.emit('receiverError', message.data);
        break;
      case 'ready':
        this.emit('receiverReady');
        break;
      case 'loadMedia':
        console.log('Media loaded on receiver');
        break;
      case 'play':
        console.log('Play command acknowledged');
        break;
      case 'pause':
        console.log('Pause command acknowledged');
        break;
      case 'seek':
        console.log('Seek command acknowledged');
        break;
      case 'volume':
        console.log('Volume command acknowledged');
        break;
      case 'subtitle':
        console.log('Subtitle command acknowledged');
        break;
      case 'stop':
        console.log('Stop command acknowledged');
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  // Media control methods
  async castMedia(mediaInfo) {
    if (!this.isConnected()) {
      throw new Error('No device connected');
    }

    const message = {
      type: 'loadMedia',
      data: {
        mediaUrl: mediaInfo.streamUrl,
        title: mediaInfo.title,
        subtitle: mediaInfo.subtitle || '',
        poster: mediaInfo.poster || '',
        subtitles: mediaInfo.subtitles || [],
        mediaType: mediaInfo.mediaType || 'movie',
        season: mediaInfo.season,
        episode: mediaInfo.episode
      }
    };

    return this.sendMessage(message);
  }

  play() {
    return this.sendMessage({ type: 'play' });
  }

  pause() {
    return this.sendMessage({ type: 'pause' });
  }

  seek(time) {
    return this.sendMessage({ type: 'seek', data: { time } });
  }

  setVolume(volume) {
    return this.sendMessage({ type: 'volume', data: { volume } });
  }

  setSubtitle(subtitleIndex) {
    return this.sendMessage({ type: 'subtitle', data: { index: subtitleIndex } });
  }

  stop() {
    return this.sendMessage({ type: 'stop' });
  }

  // Utility methods
  isConnected() {
    return this.websocket && this.websocket.readyState === WebSocket.OPEN;
  }

  getConnectedDevice() {
    return this.connectedDevice;
  }

  getDiscoveredDevices() {
    return this.discoveredDevices;
  }

  stopScanning() {
    this.isScanning = false;
    if (this.scanTimeout) {
      clearTimeout(this.scanTimeout);
      this.scanTimeout = null;
    }
  }
}

// Export singleton instance
const castingService = new CastingService();
export default castingService;
