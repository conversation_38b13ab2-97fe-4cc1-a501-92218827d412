import React, { memo, useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import { getProviderById, getProviderContentSections } from '../utils/providers';
import MediaRow from '../components/MediaRow';
import GlassBottomNav from '../components/GlassBottomNav';
import tmdbApi from '../services/tmdbApi';

// A placeholder in case the providerId is invalid or not found.
const NotFoundProvider = {
  id: 'not-found',
  name: 'Not Found',
  networkId: 0,
  LogoComponent: () => <Text style={{ color: 'white', fontSize: 24 }}>?</Text>,
  hasOriginals: false,
  theme: {
    primary: '#FFFFFF',
    gradientStart: '#333333',
    gradientEnd: '#111111',
    background: '#000000',
  },
};

const ProviderDetailScreen = memo(({ route, navigation }) => {
  const { providerId } = route.params;
  const insets = useSafeAreaInsets();

  // Look up the full provider object using the serializable ID.
  const provider = useMemo(() => getProviderById(providerId) || NotFoundProvider, [providerId]);
  const { LogoComponent } = provider;

  const [activeSection, setActiveSection] = useState('home');
  const [contentData, setContentData] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Dynamically generate the bottom nav sections based on the provider.
  const contentSections = useMemo(() => getProviderContentSections(provider), [provider]);

  // Stable data fetching function that guarantees loading state is updated.
  const loadContent = useCallback(async () => {
    // We don't set loading true here, but inside onRefresh or the initial useEffect.
    try {
      if (!provider.networkId) {
        setContentData({ providerMovies: [], providerTV: [] });
        return;
      }

      const promises = [
        tmdbApi.discoverMediaByNetwork('movie', provider.networkId),
        tmdbApi.discoverMediaByNetwork('tv', provider.networkId),
      ];

      const [movieData, tvData] = await Promise.all(
        promises.map(p => p.catch(() => ({ results: [] })))
      );

      setContentData({
        providerMovies: movieData.results,
        providerTV: tvData.results,
      });

    } catch (error) {
      console.error('Error loading provider content:', error);
      setContentData({ providerMovies: [], providerTV: [] });
    } finally {
      // This is guaranteed to run, preventing infinite loading.
      if (loading) setLoading(false);
      if (refreshing) setRefreshing(false);
    }
  }, [provider.networkId]);

  // Initial load effect
  useEffect(() => {
    setLoading(true);
    loadContent();
  }, [loadContent]);

  // Refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadContent();
  }, [loadContent]);


  const handleItemPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('MediaDetail', { item, mediaType });
  };

  const handleBackPress = () => navigation.goBack();
  const handleSectionChange = (section) => setActiveSection(section);

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.activityContainer}>
          <ActivityIndicator size="large" color={provider.theme.primary} />
          <Text style={styles.infoText}>Loading {provider.name} content...</Text>
        </View>
      );
    }

    const { providerMovies, providerTV } = contentData;
    const sectionsToRender = [];

    if (activeSection === 'home') {
      if (providerMovies?.length > 0) sectionsToRender.push(<MediaRow key="movies" title={`${provider.name} Movies`} data={providerMovies} onItemPress={handleItemPress} />);
      if (providerTV?.length > 0) sectionsToRender.push(<MediaRow key="tv" title={`${provider.name} TV Shows`} data={providerTV} onItemPress={handleItemPress} />);
    } else if (activeSection === 'movies' && providerMovies?.length > 0) {
      sectionsToRender.push(<MediaRow key="all-movies" title="All Movies" data={providerMovies} onItemPress={handleItemPress} />);
    } else if (activeSection === 'tv' && providerTV?.length > 0) {
      sectionsToRender.push(<MediaRow key="all-tv" title="All TV Shows" data={providerTV} onItemPress={handleItemPress} />);
    } else if (activeSection === 'originals' && providerTV?.length > 0) {
      sectionsToRender.push(<MediaRow key="originals" title="Originals & Exclusives" data={providerTV} onItemPress={handleItemPress} />);
    }

    if (sectionsToRender.length === 0) {
      return (
        <View style={styles.activityContainer}>
          <Text style={styles.infoText}>No content found for this section.</Text>
        </View>
      );
    }

    return sectionsToRender;
  };

  return (
    <View style={[slothStyles.container, { backgroundColor: provider.theme.background }]}>
      <StatusBar barStyle="light-content" />

      {/* Header: Positioned normally in the layout flow. */}
      <LinearGradient
        colors={[provider.theme.gradientStart, provider.theme.gradientEnd]}
        style={[styles.header, { paddingTop: insets.top }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
        <View style={styles.logoContainer}>
          <LogoComponent />
        </View>
        <View style={styles.headerSpacer} />
      </LinearGradient>

      {/* ScrollView: Renders content below the header. */}
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 120 }} // Space for bottom nav
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={provider.theme.primary}
          />
        }
      >
        {renderContent()}
      </ScrollView>

      {/* Navigation: Stays at the bottom. */}
      <GlassBottomNav
        sections={contentSections}
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        providerTheme={provider.theme}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingBottom: 10,
    height: 100, // Fixed height provides layout stability
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(25, 25, 25, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    flex: 1,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerSpacer: {
    width: 40,
    height: 40,
  },
  activityContainer: {
    paddingTop: 100,
    alignItems: 'center',
    justifyContent: 'center'
  },
  infoText: {
    color: SLOTH_COLORS.textSecondary,
    fontSize: 16,
    marginTop: 15,
  },
});

export default ProviderDetailScreen;