import NetflixLogo from '../../assets/logos/netflix.svg';
import DisneyLogo from '../../assets/logos/disney.svg';
import Hulu<PERSON>ogo from '../../assets/logos/hulu.svg';
import PrimeLogo from '../../assets/logos/prime.svg';
import Hbo<PERSON>ogo from '../../assets/logos/hbo.svg';

/**
 * A collection of theme objects for each streaming provider,
 * meticulously aligned with their official brand guidelines.
 */
const PROVIDER_THEMES = {
  netflix: {
    // GUIDELINE: Solid #E50914 logo on a pure black background. No gradients.
    primary: '#E50914',
    gradientStart: '#141414',
    gradientEnd: '#000000',
  },
  disney: {
    // GUIDELINE: Swoosh uses a subtle aurora-like gradient (bright to dark teal/blue).
    primary: '#FFFFFF',
    gradientStart: '#3E8CE0',
    gradientEnd: '#0C2248',
  },
  hulu: {
    // GUIDELINE: Hulu green logo on a dark gradient background.
    primary: '#1CE783',
    gradientStart: '#040405',
    gradientEnd: '#183949',
  },
  prime: {
    // GUIDELINE: Solid logo, no gradient defined. We'll use flat navy background.
    primary: '#00A8E1',
    gradientStart: '#0F171E',
    gradientEnd: '#0F171E',
  },
  hbo: {
    // GUIDELINE: Solid logo. Purple is used for HBO Max identity.
    primary: '#FFFFFF',
    gradientStart: '#4A0D99',
    gradientEnd: '#4A0D99',
  },
};

/**
 * The master list of streaming providers, combining data with their respective
 * SVG logo components for clean rendering.
 */
const PROVIDERS_DATA = [
  {
    id: 'netflix',
    name: 'Netflix',
    networkId: 213, // THE FIX: This is the numeric ID for the API. [5]
    LogoComponent: () => <NetflixLogo width={120} height={45} />,
    description: 'A vast library of movies, TV shows, and award-winning originals.',
    hasOriginals: true,
    theme: PROVIDER_THEMES.netflix,
  },
  {
    id: 'disney',
    name: 'Disney+',
    networkId: 2739, // THE FIX: This is the numeric ID for the API.
    LogoComponent: () => <DisneyLogo width={100} height={55} />,
    description: 'The home of Disney, Pixar, Marvel, Star Wars, and Nat Geo.',
    hasOriginals: true,
    theme: PROVIDER_THEMES.disney,
  },
  {
    id: 'hulu',
    name: 'Hulu',
    networkId: 453, // THE FIX: This is the numeric ID for the API.
    LogoComponent: () => <HuluLogo width={90} height={30} />,
    description: 'Watch current-season episodes, originals, and hit movies.',
    hasOriginals: true,
    theme: PROVIDER_THEMES.hulu,
  },
  {
    id: 'prime',
    name: 'Prime Video',
    networkId: 1024, // THE FIX: This is the numeric ID for the API. [1, 2]
    LogoComponent: () => <PrimeLogo width={130} height={40} />,
    description: 'Included with your Prime membership, featuring Amazon Originals.',
    hasOriginals: true,
    theme: PROVIDER_THEMES.prime,
  },
  {
    id: 'hbo',
    name: 'HBO Max', // Name is kept for branding, but API uses the "Max" ID now.
    networkId: 3186, // THE FIX: This is the numeric ID for the API.
    LogoComponent: () => <HboLogo width={110} height={25} />,
    description: 'Stream all of HBO, plus hit series, movies, and Max Originals.',
    hasOriginals: true,
    theme: PROVIDER_THEMES.hbo,
  },
];

/**
 * @returns {Array} A list of all available streaming providers.
 */
/**
 * @returns {Array} A list of all available streaming providers.
 */
export const getProviders = () => {
  return PROVIDERS_DATA;
};

// --- NEW FUNCTION TO FIX NAVIGATION ---
/**
 * Finds and returns a full provider object by its unique ID.
 * @param {string} id - The provider's ID (e.g., 'netflix').
 * @returns {object|undefined} The full provider object or undefined if not found.
 */
export const getProviderById = (id) => {
  return PROVIDERS_DATA.find((p) => p.id === id);
};

/**
 * **NEWLY ADDED FUNCTION**
 * Returns the navigation sections available for a specific provider.
 * This makes the bottom navigation truly dynamic.
 * @param {object} provider - The provider object.
 * @returns {Array} A list of section objects for the bottom nav.
 */
export const getProviderContentSections = (provider) => {
  const sections = [
    { id: 'home', label: 'Home', icon: 'home' },
    { id: 'movies', label: 'Movies', icon: 'film' },
    { id: 'tv', label: 'TV Shows', icon: 'tv' },
  ];

  if (provider.hasOriginals) {
    sections.push({ id: 'originals', label: 'Originals', icon: 'star' });
  }
  
  return sections;
};