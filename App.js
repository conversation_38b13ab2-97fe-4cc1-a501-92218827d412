import React, { useEffect } from 'react'; // Import useEffect
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';
import * as NavigationBar from 'expo-navigation-bar'; // Import NavigationBar
import { SLOTH_COLORS } from './src/styles/sloth'; // Use your new slothUI colors

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import SearchScreen from './src/screens/SearchScreen';
import MediaDetailScreen from './src/screens/MediaDetailScreen';
import TVEpisodesScreen from './src/screens/TVEpisodesScreen';
import PlayerScreen from './src/screens/PlayerScreen';
import MyListScreen from './src/screens/MyListScreen';
import ExploreScreen from './src/screens/ExploreScreen';
import CastDetailScreen from './src/screens/CastDetailScreen';
import ProvidersListScreen from './src/screens/ProvidersListScreen';
import ProviderDetailScreen from './src/screens/ProviderDetailScreen';
import DeviceDiscoveryScreen from './src/screens/DeviceDiscoveryScreen';
import RemoteControlScreen from './src/screens/RemoteControlScreen';

const Stack = createStackNavigator();

export default function App() {

  // This hook sets the Android navigation bar color when the app starts
  useEffect(() => {
    if (Platform.OS === 'android') {
      NavigationBar.setBackgroundColorAsync(SLOTH_COLORS.background);
      NavigationBar.setButtonStyleAsync('light');
    }
  }, []);

  return (
    <NavigationContainer>
      <StatusBar
        style="light" // Set to "light" for light text/icons on a dark background
        backgroundColor={SLOTH_COLORS.background} // Use the new theme color
        translucent={Platform.OS === 'android'}
      />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: { backgroundColor: SLOTH_COLORS.background }, // Use the new theme color
          animationEnabled: true,
          gestureEnabled: true,
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="Search"
          component={SearchScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="MediaDetail"
          component={MediaDetailScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="TVEpisodes"
          component={TVEpisodesScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="Player"
          component={PlayerScreen}
          options={{
            animationTypeForReplace: 'push',
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name="MyList"
          component={MyListScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="Explore"
          component={ExploreScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="CastDetail"
          component={CastDetailScreen}
          options={{
            animationTypeForReplace: 'push',
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name="Providers"
          component={ProvidersListScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="ProviderDetail"
          component={ProviderDetailScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="DeviceDiscovery"
          component={DeviceDiscoveryScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="RemoteControl"
          component={RemoteControlScreen}
          options={{
            animationTypeForReplace: 'push',
            gestureEnabled: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}